{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM", "DOM.Iterable"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "jsx": "react-jsx"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "build"]}