{"name": "@agent-director/desktop", "version": "1.0.0", "description": "Agent Director Desktop Application", "main": "dist/main.js", "scripts": {"dev": "concurrently \"npm run build:watch\" \"npm run electron:dev\"", "build": "npm run build:main && npm run build:renderer", "build:main": "tsc -p tsconfig.main.json", "build:renderer": "webpack --mode production", "build:watch": "concurrently \"npm run build:main -- --watch\" \"npm run build:renderer -- --watch\"", "electron:dev": "wait-on dist/main.js && electron .", "electron": "electron .", "pack": "npm run build && electron-builder --dir", "dist": "npm run build && electron-builder", "clean": "rm -rf dist build"}, "dependencies": {"@agent-director/shared": "1.0.0", "@agent-director/types": "1.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "sqlite3": "^5.1.6", "electron-store": "^8.1.0", "lucide-react": "^0.468.0"}, "devDependencies": {"@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/node": "^20.0.0", "typescript": "^5.0.0", "electron": "^28.0.0", "electron-builder": "^24.9.1", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "html-webpack-plugin": "^5.6.0", "ts-loader": "^9.5.1", "css-loader": "^6.8.1", "style-loader": "^3.3.3", "concurrently": "^8.2.2", "wait-on": "^7.2.0"}, "build": {"appId": "com.agentdirector.desktop", "productName": "Agent Director", "directories": {"output": "build"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}