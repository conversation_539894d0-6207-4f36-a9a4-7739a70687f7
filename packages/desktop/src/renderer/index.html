<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Director</title>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        #root {
            height: 100vh;
        }
        
        /* Tailwind-like utility classes for basic styling */
        .min-h-screen { min-height: 100vh; }
        .flex { display: flex; }
        .items-center { align-items: center; }
        .justify-center { justify-content: center; }
        .bg-gray-50 { background-color: #f9fafb; }
        .text-center { text-align: center; }
        .text-xl { font-size: 1.25rem; }
        .font-semibold { font-weight: 600; }
        .text-gray-900 { color: #111827; }
        .mb-4 { margin-bottom: 1rem; }
        .animate-spin {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .spinner {
            width: 2rem;
            height: 2rem;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="min-h-screen flex items-center justify-center bg-gray-50">
            <div class="text-center">
                <div class="spinner animate-spin mb-4"></div>
                <div class="text-xl font-semibold text-gray-900">Loading Agent Director...</div>
            </div>
        </div>
    </div>
</body>
</html>
