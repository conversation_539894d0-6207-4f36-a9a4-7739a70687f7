import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  useLLMProviders,
  useAgent,
  useChat
} from '@agent-director/shared';
import { Agent, ChatThread } from '@agent-director/types';
import { Plus, Bot, MessageCircle, Settings } from 'lucide-react';

// Get the desktop database adapter from the preload script
declare global {
  interface Window {
    electronAPI: {
      database: any;
      llm: any;
      platform: string;
    };
  }
}

const database = window.electronAPI?.database;

export function App() {
  const { providers } = useLLMProviders();
  const { agents, createAgent, updateAgent, deleteAgent, loading: agentsLoading } = useAgent(database);
  
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const { 
    threads, 
    currentThread, 
    messages, 
    streamingMessage,
    createNewThread, 
    selectThread, 
    deleteThread, 
    sendMessage,
    loading: chatLoading 
  } = useChat(database, selectedAgent);

  const [showAgentForm, setShowAgentForm] = useState(false);
  const [editingAgent, setEditingAgent] = useState<Agent | null>(null);
  const [activeTab, setActiveTab] = useState<'agents' | 'chat'>('agents');

  const handleCreateAgent = async (data: any) => {
    try {
      const agent = await createAgent(data);
      setShowAgentForm(false);
      setSelectedAgent(agent);
      setActiveTab('chat');
    } catch (error) {
      console.error('Failed to create agent:', error);
    }
  };

  const handleUpdateAgent = async (data: any) => {
    if (!editingAgent) return;
    
    try {
      await updateAgent(editingAgent.agent_id, data);
      setEditingAgent(null);
      setShowAgentForm(false);
    } catch (error) {
      console.error('Failed to update agent:', error);
    }
  };

  const handleDeleteAgent = async (agentId: string) => {
    if (!confirm('Are you sure you want to delete this agent?')) return;
    
    try {
      await deleteAgent(agentId);
      if (selectedAgent?.agent_id === agentId) {
        setSelectedAgent(null);
        setActiveTab('agents');
      }
    } catch (error) {
      console.error('Failed to delete agent:', error);
    }
  };

  const handleChatWithAgent = async (agent: Agent) => {
    setSelectedAgent(agent);
    setActiveTab('chat');
    
    // Create new thread if none exists
    if (threads.length === 0) {
      await createNewThread();
    }
  };

  const handleSendMessage = async (content: string) => {
    if (!currentThread) {
      await createNewThread();
    }
    await sendMessage(content);
  };

  if (!database) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-xl font-semibold text-gray-900 mb-4">
            Error: Desktop API not available
          </div>
          <div className="text-gray-600">
            Please run this app through Electron
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <Bot size={32} className="text-blue-600 mr-2" />
              <h1 className="text-xl font-semibold text-gray-900">Agent Director</h1>
            </div>
            
            <div className="text-sm text-gray-600">
              Desktop Application
            </div>
          </div>
        </div>
      </header>

      <div className="flex h-full">
        {/* Sidebar */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          {/* Navigation */}
          <div className="border-b border-gray-200">
            <nav className="flex">
              <button
                onClick={() => setActiveTab('agents')}
                className={`flex-1 py-3 px-4 text-sm font-medium ${
                  activeTab === 'agents'
                    ? 'text-blue-600 border-b-2 border-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <Settings size={16} className="inline mr-2" />
                Agents
              </button>
              <button
                onClick={() => setActiveTab('chat')}
                className={`flex-1 py-3 px-4 text-sm font-medium ${
                  activeTab === 'chat'
                    ? 'text-blue-600 border-b-2 border-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                disabled={!selectedAgent}
              >
                <MessageCircle size={16} className="inline mr-2" />
                Chat
              </button>
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 p-4 overflow-y-auto">
            {activeTab === 'agents' && (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-lg font-semibold">Your Agents</h2>
                  <Button
                    size="sm"
                    onClick={() => setShowAgentForm(true)}
                  >
                    <Plus size={16} className="mr-1" />
                    New
                  </Button>
                </div>
                
                <AgentList
                  agents={agents}
                  onEdit={(agent) => {
                    setEditingAgent(agent);
                    setShowAgentForm(true);
                  }}
                  onDelete={handleDeleteAgent}
                  onChat={handleChatWithAgent}
                  loading={agentsLoading}
                />
              </div>
            )}

            {activeTab === 'chat' && selectedAgent && (
              <div>
                <div className="mb-4">
                  <h2 className="text-lg font-semibold mb-2">Conversations</h2>
                  <p className="text-sm text-gray-600 mb-4">
                    Chatting with {selectedAgent.agent_name}
                  </p>
                </div>
                
                <ChatThreadList
                  threads={threads}
                  currentThreadId={currentThread?.chat_id}
                  onSelectThread={selectThread}
                  onDeleteThread={deleteThread}
                  loading={chatLoading}
                />
              </div>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {activeTab === 'agents' && (
            <div className="flex-1 p-6">
              <h2 className="text-2xl font-bold mb-6">Agent Management</h2>
              <p className="text-gray-600 mb-8">
                Create and manage your AI agents. Each agent can have its own personality, 
                knowledge, and behavior defined through system instructions.
              </p>
              
              {agents.length === 0 && !agentsLoading && (
                <div className="text-center py-12">
                  <Bot size={64} className="mx-auto text-gray-300 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    No agents yet
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Create your first AI agent to get started
                  </p>
                  <Button onClick={() => setShowAgentForm(true)}>
                    <Plus size={16} className="mr-2" />
                    Create Your First Agent
                  </Button>
                </div>
              )}
            </div>
          )}

          {activeTab === 'chat' && selectedAgent && (
            <div className="flex-1">
              <ChatInterface
                agent={selectedAgent}
                currentChat={currentThread}
                messages={messages}
                onSendMessage={handleSendMessage}
                onNewChat={createNewThread}
                loading={chatLoading}
                streamingMessage={streamingMessage}
              />
            </div>
          )}

          {activeTab === 'chat' && !selectedAgent && (
            <div className="flex-1 p-6">
              <div className="text-center py-12">
                <MessageCircle size={64} className="mx-auto text-gray-300 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Select an agent to chat
                </h3>
                <p className="text-gray-600">
                  Choose an agent from the sidebar to start a conversation
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Agent Form Modal */}
      <Modal
        isOpen={showAgentForm}
        onClose={() => {
          setShowAgentForm(false);
          setEditingAgent(null);
        }}
        title={editingAgent ? 'Edit Agent' : 'Create New Agent'}
        size="lg"
      >
        <AgentForm
          agent={editingAgent || undefined}
          providers={providers}
          onSubmit={editingAgent ? handleUpdateAgent : handleCreateAgent}
          onCancel={() => {
            setShowAgentForm(false);
            setEditingAgent(null);
          }}
        />
      </Modal>
    </div>
  );
}
