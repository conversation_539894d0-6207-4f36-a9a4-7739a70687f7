import { LLMClient, ChatCompletionRequest, ChatCompletionResponse } from '@agent-director/shared';

export class LLMService {
  async createChatCompletion(request: {
    apiKey: string;
    provider: string;
    model: string;
    messages: Array<{ role: string; content: string }>;
  }): Promise<ChatCompletionResponse> {
    const client = new LLMClient(request.apiKey, request.provider);
    
    return await client.createChatCompletion({
      model: request.model,
      messages: request.messages as any,
      stream: false
    });
  }

  async *streamChatCompletion(request: {
    apiKey: string;
    provider: string;
    model: string;
    messages: Array<{ role: string; content: string }>;
  }): AsyncGenerator<string, void, unknown> {
    const client = new LLMClient(request.apiKey, request.provider);
    
    for await (const chunk of client.streamChatCompletion({
      model: request.model,
      messages: request.messages as any,
      stream: true
    })) {
      yield chunk;
    }
  }
}
