import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { DatabaseAdapter } from '@agent-director/types';

// Desktop-specific database adapter that communicates with main process
class ElectronDatabaseAdapter implements DatabaseAdapter {
  async createAgent(agent: any) {
    return await ipc<PERSON>enderer.invoke('agent:create', agent);
  }

  async getAgent(agentId: string) {
    return await ipcRenderer.invoke('agent:get', agentId);
  }

  async listAgents() {
    return await ipcRenderer.invoke('agent:list');
  }

  async updateAgent(agentId: string, updates: any) {
    return await ipcRenderer.invoke('agent:update', agentId, updates);
  }

  async deleteAgent(agentId: string) {
    return await ipcRenderer.invoke('agent:delete', agentId);
  }

  async createChat(chat: any) {
    return await ipcRenderer.invoke('chat:create', chat);
  }

  async getChat(chatId: string) {
    return await ipc<PERSON>enderer.invoke('chat:get', chatId);
  }

  async listChats(agentId: string) {
    return await ipc<PERSON>enderer.invoke('chat:list', agentId);
  }

  async deleteChat(chatId: string) {
    return await ipcRenderer.invoke('chat:delete', chatId);
  }

  async addMessage(message: any) {
    return await ipcRenderer.invoke('message:add', message);
  }

  async getMessages(chatId: string) {
    return await ipcRenderer.invoke('message:list', chatId);
  }

  async deleteMessages(chatId: string) {
    return await ipcRenderer.invoke('message:delete', chatId);
  }
}

// LLM service for desktop
class ElectronLLMService {
  async createChatCompletion(request: any) {
    return await ipcRenderer.invoke('llm:chat', request);
  }

  async streamChatCompletion(request: any, onChunk: (chunk: string) => void, onDone: () => void) {
    // Set up listeners for streaming
    const handleChunk = (_: any, chunk: string) => onChunk(chunk);
    const handleDone = () => {
      onDone();
      ipcRenderer.removeListener('llm:stream-chunk', handleChunk);
      ipcRenderer.removeListener('llm:stream-done', handleDone);
    };

    ipcRenderer.on('llm:stream-chunk', handleChunk);
    ipcRenderer.on('llm:stream-done', handleDone);

    // Start streaming
    await ipcRenderer.invoke('llm:chat-stream', request);
  }
}

// Expose APIs to renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  database: new ElectronDatabaseAdapter(),
  llm: new ElectronLLMService(),
  platform: 'desktop'
});
