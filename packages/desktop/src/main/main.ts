import { app, BrowserWindow, ipcMain } from 'electron';
import * as path from 'path';
import { DatabaseManager } from './database';
import { LLMService } from './llm-service';

class App {
  private mainWindow: BrowserWindow | null = null;
  private database: DatabaseManager;
  private llmService: LLMService;

  constructor() {
    this.database = new DatabaseManager();
    this.llmService = new LLMService();
  }

  private createWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
      },
      titleBarStyle: 'default',
      show: false,
    });

    const isDev = process.env.NODE_ENV === 'development';
    
    if (isDev) {
      this.mainWindow.loadURL('http://localhost:8080');
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, 'index.html'));
    }

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
  }

  private setupIpcHandlers(): void {
    // Agent operations
    ipcMain.handle('agent:create', async (_, agentData) => {
      return await this.database.createAgent(agentData);
    });

    ipcMain.handle('agent:list', async () => {
      return await this.database.listAgents();
    });

    ipcMain.handle('agent:get', async (_, agentId) => {
      return await this.database.getAgent(agentId);
    });

    ipcMain.handle('agent:update', async (_, agentId, updates) => {
      return await this.database.updateAgent(agentId, updates);
    });

    ipcMain.handle('agent:delete', async (_, agentId) => {
      return await this.database.deleteAgent(agentId);
    });

    // Chat operations
    ipcMain.handle('chat:create', async (_, chatData) => {
      return await this.database.createChat(chatData);
    });

    ipcMain.handle('chat:list', async (_, agentId) => {
      return await this.database.listChats(agentId);
    });

    ipcMain.handle('chat:get', async (_, chatId) => {
      return await this.database.getChat(chatId);
    });

    ipcMain.handle('chat:delete', async (_, chatId) => {
      return await this.database.deleteChat(chatId);
    });

    // Message operations
    ipcMain.handle('message:add', async (_, messageData) => {
      return await this.database.addMessage(messageData);
    });

    ipcMain.handle('message:list', async (_, chatId) => {
      return await this.database.getMessages(chatId);
    });

    ipcMain.handle('message:delete', async (_, chatId) => {
      return await this.database.deleteMessages(chatId);
    });

    // LLM operations
    ipcMain.handle('llm:chat', async (_, request) => {
      return await this.llmService.createChatCompletion(request);
    });

    // Streaming chat
    ipcMain.handle('llm:chat-stream', async (event, request) => {
      const stream = this.llmService.streamChatCompletion(request);
      
      for await (const chunk of stream) {
        event.sender.send('llm:stream-chunk', chunk);
      }
      
      event.sender.send('llm:stream-done');
    });
  }

  public async initialize(): Promise<void> {
    await app.whenReady();
    
    await this.database.initialize();
    this.setupIpcHandlers();
    this.createWindow();

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow();
      }
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });
  }
}

const application = new App();
application.initialize().catch(console.error);
