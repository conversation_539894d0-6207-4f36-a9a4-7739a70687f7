import { Database } from 'sqlite3';
import { app } from 'electron';
import * as path from 'path';
import { randomUUID } from 'crypto';
import { DatabaseAdapter, Agent, ChatThread, Message } from '@agent-director/types';

export class DatabaseManager implements DatabaseAdapter {
  private db: Database | null = null;
  private dbPath: string;

  constructor() {
    const userDataPath = app.getPath('userData');
    this.dbPath = path.join(userDataPath, 'agent-director.db');
  }

  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db = new Database(this.dbPath, (err) => {
        if (err) {
          reject(err);
          return;
        }
        
        this.createTables()
          .then(() => resolve())
          .catch(reject);
      });
    });
  }

  private async createTables(): Promise<void> {
    const queries = [
      `CREATE TABLE IF NOT EXISTS agents (
        agent_id TEXT PRIMARY KEY,
        agent_name TEXT NOT NULL,
        model_provider TEXT NOT NULL,
        model_name TEXT NOT NULL,
        api_key TEXT,
        system_instructions TEXT NOT NULL,
        creation_date TEXT NOT NULL,
        last_modified_date TEXT NOT NULL
      )`,
      
      `CREATE TABLE IF NOT EXISTS chat_threads (
        chat_id TEXT PRIMARY KEY,
        agent_id TEXT NOT NULL,
        start_time TEXT NOT NULL,
        last_updated_time TEXT NOT NULL,
        title TEXT,
        FOREIGN KEY (agent_id) REFERENCES agents (agent_id) ON DELETE CASCADE
      )`,
      
      `CREATE TABLE IF NOT EXISTS messages (
        message_id TEXT PRIMARY KEY,
        chat_id TEXT NOT NULL,
        sender TEXT NOT NULL CHECK (sender IN ('user', 'agent')),
        content TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        FOREIGN KEY (chat_id) REFERENCES chat_threads (chat_id) ON DELETE CASCADE
      )`,
      
      `CREATE INDEX IF NOT EXISTS idx_chat_threads_agent_id ON chat_threads(agent_id)`,
      `CREATE INDEX IF NOT EXISTS idx_messages_chat_id ON messages(chat_id)`
    ];

    for (const query of queries) {
      await this.runQuery(query);
    }
  }

  private runQuery(sql: string, params: any[] = []): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ lastID: this.lastID, changes: this.changes });
        }
      });
    });
  }

  private getQuery(sql: string, params: any[] = []): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      this.db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  private allQuery(sql: string, params: any[] = []): Promise<any[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  async createAgent(agent: Omit<Agent, 'agent_id' | 'creation_date' | 'last_modified_date'>): Promise<Agent> {
    const now = new Date().toISOString();
    const agentData = {
      agent_id: randomUUID(),
      ...agent,
      creation_date: now,
      last_modified_date: now
    };

    await this.runQuery(
      `INSERT INTO agents (agent_id, agent_name, model_provider, model_name, api_key, system_instructions, creation_date, last_modified_date)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        agentData.agent_id,
        agentData.agent_name,
        agentData.model_provider,
        agentData.model_name,
        agentData.api_key,
        agentData.system_instructions,
        agentData.creation_date,
        agentData.last_modified_date
      ]
    );

    return {
      ...agentData,
      creation_date: new Date(agentData.creation_date),
      last_modified_date: new Date(agentData.last_modified_date)
    };
  }

  async getAgent(agentId: string): Promise<Agent | null> {
    const row = await this.getQuery(
      'SELECT * FROM agents WHERE agent_id = ?',
      [agentId]
    );

    if (!row) return null;

    return {
      ...row,
      creation_date: new Date(row.creation_date),
      last_modified_date: new Date(row.last_modified_date)
    };
  }

  async listAgents(): Promise<Agent[]> {
    const rows = await this.allQuery(
      'SELECT * FROM agents ORDER BY last_modified_date DESC'
    );

    return rows.map(row => ({
      ...row,
      creation_date: new Date(row.creation_date),
      last_modified_date: new Date(row.last_modified_date)
    }));
  }

  async updateAgent(agentId: string, updates: Partial<Agent>): Promise<Agent> {
    const updateData = {
      ...updates,
      last_modified_date: new Date().toISOString()
    };

    const setClause = Object.keys(updateData)
      .map(key => `${key} = ?`)
      .join(', ');
    
    const values = [...Object.values(updateData), agentId];

    await this.runQuery(
      `UPDATE agents SET ${setClause} WHERE agent_id = ?`,
      values
    );

    const updatedAgent = await this.getAgent(agentId);
    if (!updatedAgent) {
      throw new Error('Agent not found after update');
    }

    return updatedAgent;
  }

  async deleteAgent(agentId: string): Promise<boolean> {
    const result = await this.runQuery(
      'DELETE FROM agents WHERE agent_id = ?',
      [agentId]
    );

    return result.changes > 0;
  }

  async createChat(chat: Omit<ChatThread, 'chat_id' | 'start_time' | 'last_updated_time'>): Promise<ChatThread> {
    const now = new Date().toISOString();
    const chatData = {
      chat_id: randomUUID(),
      ...chat,
      start_time: now,
      last_updated_time: now
    };

    await this.runQuery(
      `INSERT INTO chat_threads (chat_id, agent_id, start_time, last_updated_time, title)
       VALUES (?, ?, ?, ?, ?)`,
      [
        chatData.chat_id,
        chatData.agent_id,
        chatData.start_time,
        chatData.last_updated_time,
        chatData.title
      ]
    );

    return {
      ...chatData,
      start_time: new Date(chatData.start_time),
      last_updated_time: new Date(chatData.last_updated_time)
    };
  }

  async getChat(chatId: string): Promise<ChatThread | null> {
    const row = await this.getQuery(
      'SELECT * FROM chat_threads WHERE chat_id = ?',
      [chatId]
    );

    if (!row) return null;

    return {
      ...row,
      start_time: new Date(row.start_time),
      last_updated_time: new Date(row.last_updated_time)
    };
  }

  async listChats(agentId: string): Promise<ChatThread[]> {
    const rows = await this.allQuery(
      'SELECT * FROM chat_threads WHERE agent_id = ? ORDER BY last_updated_time DESC',
      [agentId]
    );

    return rows.map(row => ({
      ...row,
      start_time: new Date(row.start_time),
      last_updated_time: new Date(row.last_updated_time)
    }));
  }

  async deleteChat(chatId: string): Promise<boolean> {
    // Delete messages first (cascade should handle this, but being explicit)
    await this.runQuery('DELETE FROM messages WHERE chat_id = ?', [chatId]);
    
    const result = await this.runQuery(
      'DELETE FROM chat_threads WHERE chat_id = ?',
      [chatId]
    );

    return result.changes > 0;
  }

  async addMessage(message: Omit<Message, 'message_id' | 'timestamp'>): Promise<Message> {
    const messageData = {
      message_id: randomUUID(),
      ...message,
      timestamp: new Date().toISOString()
    };

    await this.runQuery(
      `INSERT INTO messages (message_id, chat_id, sender, content, timestamp)
       VALUES (?, ?, ?, ?, ?)`,
      [
        messageData.message_id,
        messageData.chat_id,
        messageData.sender,
        messageData.content,
        messageData.timestamp
      ]
    );

    // Update chat thread's last_updated_time
    await this.runQuery(
      'UPDATE chat_threads SET last_updated_time = ? WHERE chat_id = ?',
      [messageData.timestamp, message.chat_id]
    );

    return {
      ...messageData,
      timestamp: new Date(messageData.timestamp)
    };
  }

  async getMessages(chatId: string): Promise<Message[]> {
    const rows = await this.allQuery(
      'SELECT * FROM messages WHERE chat_id = ? ORDER BY timestamp ASC',
      [chatId]
    );

    return rows.map(row => ({
      ...row,
      timestamp: new Date(row.timestamp)
    }));
  }

  async deleteMessages(chatId: string): Promise<boolean> {
    const result = await this.runQuery(
      'DELETE FROM messages WHERE chat_id = ?',
      [chatId]
    );

    return result.changes >= 0; // Could be 0 if no messages exist
  }
}
