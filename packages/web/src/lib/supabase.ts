import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database schema types for Supabase
export interface Database {
  public: {
    Tables: {
      agents: {
        Row: {
          agent_id: string;
          user_id: string;
          agent_name: string;
          model_provider: string;
          model_name: string;
          api_key: string | null;
          system_instructions: string;
          creation_date: string;
          last_modified_date: string;
        };
        Insert: {
          agent_id?: string;
          user_id: string;
          agent_name: string;
          model_provider: string;
          model_name: string;
          api_key?: string | null;
          system_instructions: string;
          creation_date?: string;
          last_modified_date?: string;
        };
        Update: {
          agent_id?: string;
          user_id?: string;
          agent_name?: string;
          model_provider?: string;
          model_name?: string;
          api_key?: string | null;
          system_instructions?: string;
          creation_date?: string;
          last_modified_date?: string;
        };
      };
      chat_threads: {
        Row: {
          chat_id: string;
          agent_id: string;
          user_id: string;
          start_time: string;
          last_updated_time: string;
          title: string | null;
        };
        Insert: {
          chat_id?: string;
          agent_id: string;
          user_id: string;
          start_time?: string;
          last_updated_time?: string;
          title?: string | null;
        };
        Update: {
          chat_id?: string;
          agent_id?: string;
          user_id?: string;
          start_time?: string;
          last_updated_time?: string;
          title?: string | null;
        };
      };
      messages: {
        Row: {
          message_id: string;
          chat_id: string;
          sender: string;
          content: string;
          timestamp: string;
        };
        Insert: {
          message_id?: string;
          chat_id: string;
          sender: string;
          content: string;
          timestamp?: string;
        };
        Update: {
          message_id?: string;
          chat_id?: string;
          sender?: string;
          content?: string;
          timestamp?: string;
        };
      };
      shared_agent_links: {
        Row: {
          link_id: string;
          agent_id: string;
          author_user_id: string;
          share_mode: string;
          creation_date: string;
        };
        Insert: {
          link_id?: string;
          agent_id: string;
          author_user_id: string;
          share_mode: string;
          creation_date?: string;
        };
        Update: {
          link_id?: string;
          agent_id?: string;
          author_user_id?: string;
          share_mode?: string;
          creation_date?: string;
        };
      };
      users: {
        Row: {
          user_id: string;
          email: string;
          name: string;
          avatar_url: string | null;
          created_at: string;
        };
        Insert: {
          user_id: string;
          email: string;
          name: string;
          avatar_url?: string | null;
          created_at?: string;
        };
        Update: {
          user_id?: string;
          email?: string;
          name?: string;
          avatar_url?: string | null;
          created_at?: string;
        };
      };
    };
  };
}
