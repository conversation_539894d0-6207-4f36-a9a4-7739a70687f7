import { <PERSON><PERSON><PERSON>pter, Agent, ChatThread, Message } from '@agent-director/types';
import { supabase } from './supabase';
import { v4 as uuidv4 } from 'uuid';

export class SupabaseDatabaseAdapter implements DatabaseAdapter {
  async createAgent(agent: Omit<Agent, 'agent_id' | 'creation_date' | 'last_modified_date'>): Promise<Agent> {
    const now = new Date().toISOString();
    const agentData = {
      agent_id: uuidv4(),
      ...agent,
      creation_date: now,
      last_modified_date: now
    };

    const { data, error } = await supabase
      .from('agents')
      .insert(agentData)
      .select()
      .single();

    if (error) throw new Error(`Failed to create agent: ${error.message}`);

    return {
      ...data,
      creation_date: new Date(data.creation_date),
      last_modified_date: new Date(data.last_modified_date)
    };
  }

  async getAgent(agentId: string, userId?: string): Promise<Agent | null> {
    let query = supabase
      .from('agents')
      .select('*')
      .eq('agent_id', agentId);

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data, error } = await query.single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw new Error(`Failed to get agent: ${error.message}`);
    }

    return {
      ...data,
      creation_date: new Date(data.creation_date),
      last_modified_date: new Date(data.last_modified_date)
    };
  }

  async listAgents(userId?: string): Promise<Agent[]> {
    let query = supabase
      .from('agents')
      .select('*')
      .order('last_modified_date', { ascending: false });

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data, error } = await query;

    if (error) throw new Error(`Failed to list agents: ${error.message}`);

    return data.map(agent => ({
      ...agent,
      creation_date: new Date(agent.creation_date),
      last_modified_date: new Date(agent.last_modified_date)
    }));
  }

  async updateAgent(agentId: string, updates: Partial<Agent>, userId?: string): Promise<Agent> {
    const updateData = {
      ...updates,
      last_modified_date: new Date().toISOString()
    };

    let query = supabase
      .from('agents')
      .update(updateData)
      .eq('agent_id', agentId);

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data, error } = await query.select().single();

    if (error) throw new Error(`Failed to update agent: ${error.message}`);

    return {
      ...data,
      creation_date: new Date(data.creation_date),
      last_modified_date: new Date(data.last_modified_date)
    };
  }

  async deleteAgent(agentId: string, userId?: string): Promise<boolean> {
    let query = supabase
      .from('agents')
      .delete()
      .eq('agent_id', agentId);

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { error } = await query;

    if (error) throw new Error(`Failed to delete agent: ${error.message}`);

    return true;
  }

  async createChat(chat: Omit<ChatThread, 'chat_id' | 'start_time' | 'last_updated_time'>): Promise<ChatThread> {
    const now = new Date().toISOString();
    const chatData = {
      chat_id: uuidv4(),
      ...chat,
      start_time: now,
      last_updated_time: now
    };

    const { data, error } = await supabase
      .from('chat_threads')
      .insert(chatData)
      .select()
      .single();

    if (error) throw new Error(`Failed to create chat: ${error.message}`);

    return {
      ...data,
      start_time: new Date(data.start_time),
      last_updated_time: new Date(data.last_updated_time)
    };
  }

  async getChat(chatId: string, userId?: string): Promise<ChatThread | null> {
    let query = supabase
      .from('chat_threads')
      .select('*')
      .eq('chat_id', chatId);

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data, error } = await query.single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw new Error(`Failed to get chat: ${error.message}`);
    }

    return {
      ...data,
      start_time: new Date(data.start_time),
      last_updated_time: new Date(data.last_updated_time)
    };
  }

  async listChats(agentId: string, userId?: string): Promise<ChatThread[]> {
    let query = supabase
      .from('chat_threads')
      .select('*')
      .eq('agent_id', agentId)
      .order('last_updated_time', { ascending: false });

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data, error } = await query;

    if (error) throw new Error(`Failed to list chats: ${error.message}`);

    return data.map(chat => ({
      ...chat,
      start_time: new Date(chat.start_time),
      last_updated_time: new Date(chat.last_updated_time)
    }));
  }

  async deleteChat(chatId: string, userId?: string): Promise<boolean> {
    // First delete all messages in the chat
    await supabase
      .from('messages')
      .delete()
      .eq('chat_id', chatId);

    // Then delete the chat thread
    let query = supabase
      .from('chat_threads')
      .delete()
      .eq('chat_id', chatId);

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { error } = await query;

    if (error) throw new Error(`Failed to delete chat: ${error.message}`);

    return true;
  }

  async addMessage(message: Omit<Message, 'message_id' | 'timestamp'>): Promise<Message> {
    const messageData = {
      message_id: uuidv4(),
      ...message,
      timestamp: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('messages')
      .insert(messageData)
      .select()
      .single();

    if (error) throw new Error(`Failed to add message: ${error.message}`);

    // Update chat thread's last_updated_time
    await supabase
      .from('chat_threads')
      .update({ last_updated_time: messageData.timestamp })
      .eq('chat_id', message.chat_id);

    return {
      ...data,
      timestamp: new Date(data.timestamp)
    };
  }

  async getMessages(chatId: string, userId?: string): Promise<Message[]> {
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('chat_id', chatId)
      .order('timestamp', { ascending: true });

    if (error) throw new Error(`Failed to get messages: ${error.message}`);

    return data.map(message => ({
      ...message,
      timestamp: new Date(message.timestamp)
    }));
  }

  async deleteMessages(chatId: string, userId?: string): Promise<boolean> {
    const { error } = await supabase
      .from('messages')
      .delete()
      .eq('chat_id', chatId);

    if (error) throw new Error(`Failed to delete messages: ${error.message}`);

    return true;
  }
}
