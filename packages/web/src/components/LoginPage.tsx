'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@agent-director/shared';
import { Bot, Users, Share2, Zap } from 'lucide-react';

export function LoginPage() {
  const { signInWithGoogle } = useAuth();
  const [loading, setLoading] = useState(false);

  const handleSignIn = async () => {
    setLoading(true);
    try {
      await signInWithGoogle();
    } catch (error) {
      console.error('Sign in error:', error);
      // TODO: Show error toast
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-6">
            <Bot size={48} className="text-blue-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900">Agent Director</h1>
          </div>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Create, manage, and share AI agents with custom personalities and behaviors. 
            Build your own AI workforce and share them with the world.
          </p>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <div className="text-center p-6 bg-white rounded-lg shadow-sm">
            <Bot size={32} className="text-blue-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Create Custom Agents</h3>
            <p className="text-gray-600">
              Define AI agents with custom system instructions and choose from multiple LLM providers
            </p>
          </div>
          
          <div className="text-center p-6 bg-white rounded-lg shadow-sm">
            <Users size={32} className="text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Chat & Collaborate</h3>
            <p className="text-gray-600">
              Have conversations with your agents and manage multiple chat threads
            </p>
          </div>
          
          <div className="text-center p-6 bg-white rounded-lg shadow-sm">
            <Share2 size={32} className="text-purple-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Share & Discover</h3>
            <p className="text-gray-600">
              Share your agents with others or clone interesting agents from the community
            </p>
          </div>
        </div>

        {/* Sign In */}
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Get Started</h2>
            <p className="text-gray-600">Sign in to create and manage your AI agents</p>
          </div>
          
          <Button
            onClick={handleSignIn}
            loading={loading}
            className="w-full flex items-center justify-center"
            size="lg"
          >
            <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="currentColor"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="currentColor"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="currentColor"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            Continue with Google
          </Button>
          
          <p className="text-xs text-gray-500 text-center mt-4">
            By signing in, you agree to our Terms of Service and Privacy Policy
          </p>
        </div>

        {/* Footer */}
        <div className="text-center mt-16 text-gray-500">
          <p>&copy; 2024 Agent Director. Built with ❤️ for the AI community.</p>
        </div>
      </div>
    </div>
  );
}
