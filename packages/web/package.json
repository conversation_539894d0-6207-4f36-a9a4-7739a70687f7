{"name": "@agent-director/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@agent-director/shared": "1.0.0", "@agent-director/types": "1.0.0", "@supabase/supabase-js": "^2.39.0", "react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.3", "lucide-react": "^0.468.0", "uuid": "^9.0.1"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^9.0.7", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3"}}