(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/packages/shared/dist/components/ui/Input.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.Input = void 0;
const jsx_runtime_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const Input = ({ label, error, helperText, className = '', id, ...props })=>{
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    const inputClasses = `
    block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
    ${error ? 'border-red-300' : 'border-gray-300'}
    ${className}
  `.trim();
    return (0, jsx_runtime_1.jsxs)("div", {
        className: "space-y-1",
        children: [
            label && (0, jsx_runtime_1.jsx)("label", {
                htmlFor: inputId,
                className: "block text-sm font-medium text-gray-700",
                children: label
            }),
            (0, jsx_runtime_1.jsx)("input", {
                id: inputId,
                className: inputClasses,
                ...props
            }),
            error && (0, jsx_runtime_1.jsx)("p", {
                className: "text-sm text-red-600",
                children: error
            }),
            helperText && !error && (0, jsx_runtime_1.jsx)("p", {
                className: "text-sm text-gray-500",
                children: helperText
            })
        ]
    });
};
exports.Input = Input; //# sourceMappingURL=Input.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/components/ui/TextArea.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.TextArea = void 0;
const jsx_runtime_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const TextArea = ({ label, error, helperText, className = '', id, ...props })=>{
    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;
    const textareaClasses = `
    block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
    resize-vertical min-h-[100px]
    ${error ? 'border-red-300' : 'border-gray-300'}
    ${className}
  `.trim();
    return (0, jsx_runtime_1.jsxs)("div", {
        className: "space-y-1",
        children: [
            label && (0, jsx_runtime_1.jsx)("label", {
                htmlFor: textareaId,
                className: "block text-sm font-medium text-gray-700",
                children: label
            }),
            (0, jsx_runtime_1.jsx)("textarea", {
                id: textareaId,
                className: textareaClasses,
                ...props
            }),
            error && (0, jsx_runtime_1.jsx)("p", {
                className: "text-sm text-red-600",
                children: error
            }),
            helperText && !error && (0, jsx_runtime_1.jsx)("p", {
                className: "text-sm text-gray-500",
                children: helperText
            })
        ]
    });
};
exports.TextArea = TextArea; //# sourceMappingURL=TextArea.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/components/ui/Select.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.Select = void 0;
const jsx_runtime_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const Select = ({ label, error, helperText, options, placeholder, className = '', id, ...props })=>{
    const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;
    const selectClasses = `
    block w-full px-3 py-2 border rounded-md shadow-sm 
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
    ${error ? 'border-red-300' : 'border-gray-300'}
    ${className}
  `.trim();
    return (0, jsx_runtime_1.jsxs)("div", {
        className: "space-y-1",
        children: [
            label && (0, jsx_runtime_1.jsx)("label", {
                htmlFor: selectId,
                className: "block text-sm font-medium text-gray-700",
                children: label
            }),
            (0, jsx_runtime_1.jsxs)("select", {
                id: selectId,
                className: selectClasses,
                ...props,
                children: [
                    placeholder && (0, jsx_runtime_1.jsx)("option", {
                        value: "",
                        disabled: true,
                        children: placeholder
                    }),
                    options.map((option)=>(0, jsx_runtime_1.jsx)("option", {
                            value: option.value,
                            children: option.label
                        }, option.value))
                ]
            }),
            error && (0, jsx_runtime_1.jsx)("p", {
                className: "text-sm text-red-600",
                children: error
            }),
            helperText && !error && (0, jsx_runtime_1.jsx)("p", {
                className: "text-sm text-gray-500",
                children: helperText
            })
        ]
    });
};
exports.Select = Select; //# sourceMappingURL=Select.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/components/ui/Button.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.Button = void 0;
const jsx_runtime_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const Button = ({ children, variant = 'primary', size = 'md', loading = false, disabled, className = '', ...props })=>{
    const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
    const variantClasses = {
        primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
        secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
        danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
        ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500'
    };
    const sizeClasses = {
        sm: 'px-3 py-1.5 text-sm',
        md: 'px-4 py-2 text-sm',
        lg: 'px-6 py-3 text-base'
    };
    const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;
    return (0, jsx_runtime_1.jsxs)("button", {
        className: classes,
        disabled: disabled || loading,
        ...props,
        children: [
            loading && (0, jsx_runtime_1.jsxs)("svg", {
                className: "animate-spin -ml-1 mr-2 h-4 w-4",
                fill: "none",
                viewBox: "0 0 24 24",
                children: [
                    (0, jsx_runtime_1.jsx)("circle", {
                        className: "opacity-25",
                        cx: "12",
                        cy: "12",
                        r: "10",
                        stroke: "currentColor",
                        strokeWidth: "4"
                    }),
                    (0, jsx_runtime_1.jsx)("path", {
                        className: "opacity-75",
                        fill: "currentColor",
                        d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    })
                ]
            }),
            children
        ]
    });
};
exports.Button = Button; //# sourceMappingURL=Button.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/components/AgentForm.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AgentForm = void 0;
const jsx_runtime_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const react_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
const Input_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/ui/Input.js [app-client] (ecmascript)");
const TextArea_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/ui/TextArea.js [app-client] (ecmascript)");
const Select_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/ui/Select.js [app-client] (ecmascript)");
const Button_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/ui/Button.js [app-client] (ecmascript)");
const AgentForm = ({ agent, providers, onSubmit, onCancel, loading = false })=>{
    const [formData, setFormData] = (0, react_1.useState)({
        agent_name: agent?.agent_name || '',
        model_provider: agent?.model_provider || 'OpenAI',
        model_name: agent?.model_name || '',
        api_key: agent?.api_key || '',
        system_instructions: agent?.system_instructions || ''
    });
    const [errors, setErrors] = (0, react_1.useState)({});
    const selectedProvider = providers.find((p)=>p.name === formData.model_provider);
    const handleSubmit = async (e)=>{
        e.preventDefault();
        // Validation
        const newErrors = {};
        if (!formData.agent_name.trim()) {
            newErrors.agent_name = 'Agent name is required';
        }
        if (!formData.model_name) {
            newErrors.model_name = 'Model selection is required';
        }
        if (!formData.system_instructions.trim()) {
            newErrors.system_instructions = 'System instructions are required';
        }
        if (selectedProvider?.requiresApiKey && !formData.api_key.trim()) {
            newErrors.api_key = 'API key is required for this provider';
        }
        if (Object.keys(newErrors).length > 0) {
            setErrors(newErrors);
            return;
        }
        setErrors({});
        const submitData = agent ? {
            agent_id: agent.agent_id,
            ...formData
        } : formData;
        await onSubmit(submitData);
    };
    const handleChange = (field, value)=>{
        setFormData((prev)=>({
                ...prev,
                [field]: value
            }));
        if (errors[field]) {
            setErrors((prev)=>({
                    ...prev,
                    [field]: ''
                }));
        }
    };
    return (0, jsx_runtime_1.jsxs)("form", {
        onSubmit: handleSubmit,
        className: "space-y-6",
        children: [
            (0, jsx_runtime_1.jsx)(Input_1.Input, {
                label: "Agent Name",
                value: formData.agent_name,
                onChange: (e)=>handleChange('agent_name', e.target.value),
                error: errors.agent_name,
                placeholder: "Enter a name for your agent",
                required: true
            }),
            (0, jsx_runtime_1.jsx)(Select_1.Select, {
                label: "Model Provider",
                value: formData.model_provider,
                onChange: (e)=>handleChange('model_provider', e.target.value),
                options: providers.map((p)=>({
                        value: p.name,
                        label: p.name
                    })),
                error: errors.model_provider,
                required: true
            }),
            selectedProvider && (0, jsx_runtime_1.jsx)(Select_1.Select, {
                label: "Model",
                value: formData.model_name,
                onChange: (e)=>handleChange('model_name', e.target.value),
                options: selectedProvider.models.map((m)=>({
                        value: m.id,
                        label: m.name
                    })),
                placeholder: "Select a model",
                error: errors.model_name,
                required: true
            }),
            selectedProvider?.requiresApiKey && (0, jsx_runtime_1.jsx)(Input_1.Input, {
                label: "API Key",
                type: "password",
                value: formData.api_key,
                onChange: (e)=>handleChange('api_key', e.target.value),
                error: errors.api_key,
                placeholder: "Enter your API key",
                helperText: "Your API key will be stored securely",
                required: true
            }),
            (0, jsx_runtime_1.jsx)(TextArea_1.TextArea, {
                label: "System Instructions",
                value: formData.system_instructions,
                onChange: (e)=>handleChange('system_instructions', e.target.value),
                error: errors.system_instructions,
                placeholder: "Define how your agent should behave...",
                rows: 6,
                required: true
            }),
            (0, jsx_runtime_1.jsxs)("div", {
                className: "flex justify-end space-x-3",
                children: [
                    (0, jsx_runtime_1.jsx)(Button_1.Button, {
                        type: "button",
                        variant: "secondary",
                        onClick: onCancel,
                        children: "Cancel"
                    }),
                    (0, jsx_runtime_1.jsx)(Button_1.Button, {
                        type: "submit",
                        loading: loading,
                        children: agent ? 'Update Agent' : 'Create Agent'
                    })
                ]
            })
        ]
    });
};
exports.AgentForm = AgentForm; //# sourceMappingURL=AgentForm.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/components/AgentList.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AgentList = void 0;
const jsx_runtime_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const Button_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/ui/Button.js [app-client] (ecmascript)");
const lucide_react_1 = __turbopack_context__.r("[project]/node_modules/lucide-react/dist/esm/lucide-react.js [app-client] (ecmascript)");
const AgentList = ({ agents, onEdit, onDelete, onChat, onShare, loading = false })=>{
    if (loading) {
        return (0, jsx_runtime_1.jsx)("div", {
            className: "space-y-4",
            children: [
                ...Array(3)
            ].map((_, i)=>(0, jsx_runtime_1.jsx)("div", {
                    className: "animate-pulse",
                    children: (0, jsx_runtime_1.jsx)("div", {
                        className: "bg-gray-200 rounded-lg h-32"
                    })
                }, i))
        });
    }
    if (agents.length === 0) {
        return (0, jsx_runtime_1.jsxs)("div", {
            className: "text-center py-12",
            children: [
                (0, jsx_runtime_1.jsx)("div", {
                    className: "text-gray-500 text-lg mb-2",
                    children: "No agents created yet"
                }),
                (0, jsx_runtime_1.jsx)("div", {
                    className: "text-gray-400",
                    children: "Create your first AI agent to get started"
                })
            ]
        });
    }
    return (0, jsx_runtime_1.jsx)("div", {
        className: "space-y-4",
        children: agents.map((agent)=>(0, jsx_runtime_1.jsxs)("div", {
                className: "bg-white border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow",
                children: [
                    (0, jsx_runtime_1.jsx)("div", {
                        className: "flex justify-between items-start mb-4",
                        children: (0, jsx_runtime_1.jsxs)("div", {
                            className: "flex-1",
                            children: [
                                (0, jsx_runtime_1.jsx)("h3", {
                                    className: "text-lg font-semibold text-gray-900 mb-1",
                                    children: agent.agent_name
                                }),
                                (0, jsx_runtime_1.jsxs)("div", {
                                    className: "text-sm text-gray-500 mb-2",
                                    children: [
                                        agent.model_provider,
                                        " \u2022 ",
                                        agent.model_name
                                    ]
                                }),
                                (0, jsx_runtime_1.jsx)("p", {
                                    className: "text-gray-600 text-sm line-clamp-2",
                                    children: agent.system_instructions
                                })
                            ]
                        })
                    }),
                    (0, jsx_runtime_1.jsxs)("div", {
                        className: "flex justify-between items-center",
                        children: [
                            (0, jsx_runtime_1.jsxs)("div", {
                                className: "text-xs text-gray-400",
                                children: [
                                    "Created ",
                                    new Date(agent.creation_date).toLocaleDateString()
                                ]
                            }),
                            (0, jsx_runtime_1.jsxs)("div", {
                                className: "flex space-x-2",
                                children: [
                                    (0, jsx_runtime_1.jsx)(Button_1.Button, {
                                        size: "sm",
                                        variant: "ghost",
                                        onClick: ()=>onChat(agent),
                                        title: "Chat with agent",
                                        children: (0, jsx_runtime_1.jsx)(lucide_react_1.MessageCircle, {
                                            size: 16
                                        })
                                    }),
                                    onShare && (0, jsx_runtime_1.jsx)(Button_1.Button, {
                                        size: "sm",
                                        variant: "ghost",
                                        onClick: ()=>onShare(agent),
                                        title: "Share agent",
                                        children: (0, jsx_runtime_1.jsx)(lucide_react_1.Share, {
                                            size: 16
                                        })
                                    }),
                                    (0, jsx_runtime_1.jsx)(Button_1.Button, {
                                        size: "sm",
                                        variant: "ghost",
                                        onClick: ()=>onEdit(agent),
                                        title: "Edit agent",
                                        children: (0, jsx_runtime_1.jsx)(lucide_react_1.Edit, {
                                            size: 16
                                        })
                                    }),
                                    (0, jsx_runtime_1.jsx)(Button_1.Button, {
                                        size: "sm",
                                        variant: "ghost",
                                        onClick: ()=>onDelete(agent.agent_id),
                                        title: "Delete agent",
                                        children: (0, jsx_runtime_1.jsx)(lucide_react_1.Trash2, {
                                            size: 16
                                        })
                                    })
                                ]
                            })
                        ]
                    })
                ]
            }, agent.agent_id))
    });
};
exports.AgentList = AgentList; //# sourceMappingURL=AgentList.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/components/MessageBubble.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.MessageBubble = void 0;
const jsx_runtime_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const lucide_react_1 = __turbopack_context__.r("[project]/node_modules/lucide-react/dist/esm/lucide-react.js [app-client] (ecmascript)");
const MessageBubble = ({ message, isStreaming = false })=>{
    const isUser = message.sender === 'user';
    return (0, jsx_runtime_1.jsx)("div", {
        className: `flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`,
        children: (0, jsx_runtime_1.jsxs)("div", {
            className: `flex max-w-[80%] ${isUser ? 'flex-row-reverse' : 'flex-row'}`,
            children: [
                (0, jsx_runtime_1.jsx)("div", {
                    className: `flex-shrink-0 ${isUser ? 'ml-3' : 'mr-3'}`,
                    children: (0, jsx_runtime_1.jsx)("div", {
                        className: `w-8 h-8 rounded-full flex items-center justify-center ${isUser ? 'bg-blue-500' : 'bg-gray-500'}`,
                        children: isUser ? (0, jsx_runtime_1.jsx)(lucide_react_1.User, {
                            size: 16,
                            className: "text-white"
                        }) : (0, jsx_runtime_1.jsx)(lucide_react_1.Bot, {
                            size: 16,
                            className: "text-white"
                        })
                    })
                }),
                (0, jsx_runtime_1.jsxs)("div", {
                    className: `rounded-lg px-4 py-2 ${isUser ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-900'}`,
                    children: [
                        (0, jsx_runtime_1.jsxs)("div", {
                            className: "whitespace-pre-wrap break-words",
                            children: [
                                message.content,
                                isStreaming && (0, jsx_runtime_1.jsx)("span", {
                                    className: "inline-block w-2 h-5 bg-current ml-1 animate-pulse"
                                })
                            ]
                        }),
                        (0, jsx_runtime_1.jsx)("div", {
                            className: `text-xs mt-1 ${isUser ? 'text-blue-100' : 'text-gray-500'}`,
                            children: new Date(message.timestamp).toLocaleTimeString([], {
                                hour: '2-digit',
                                minute: '2-digit'
                            })
                        })
                    ]
                })
            ]
        })
    });
};
exports.MessageBubble = MessageBubble; //# sourceMappingURL=MessageBubble.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/components/ChatInterface.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ChatInterface = void 0;
const jsx_runtime_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const react_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
const MessageBubble_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/MessageBubble.js [app-client] (ecmascript)");
const Button_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/ui/Button.js [app-client] (ecmascript)");
const lucide_react_1 = __turbopack_context__.r("[project]/node_modules/lucide-react/dist/esm/lucide-react.js [app-client] (ecmascript)");
const ChatInterface = ({ agent, currentChat, messages, onSendMessage, onNewChat, loading = false, streamingMessage })=>{
    const [input, setInput] = (0, react_1.useState)('');
    const [sending, setSending] = (0, react_1.useState)(false);
    const messagesEndRef = (0, react_1.useRef)(null);
    const textareaRef = (0, react_1.useRef)(null);
    const scrollToBottom = ()=>{
        messagesEndRef.current?.scrollIntoView({
            behavior: 'smooth'
        });
    };
    (0, react_1.useEffect)(()=>{
        scrollToBottom();
    }, [
        messages,
        streamingMessage
    ]);
    const handleSubmit = async (e)=>{
        e.preventDefault();
        if (!input.trim() || sending) return;
        const messageContent = input.trim();
        setInput('');
        setSending(true);
        try {
            await onSendMessage(messageContent);
        } finally{
            setSending(false);
        }
    };
    const handleKeyDown = (e)=>{
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit(e);
        }
    };
    // Auto-resize textarea
    (0, react_1.useEffect)(()=>{
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
        }
    }, [
        input
    ]);
    return (0, jsx_runtime_1.jsxs)("div", {
        className: "flex flex-col h-full",
        children: [
            (0, jsx_runtime_1.jsxs)("div", {
                className: "border-b bg-white px-6 py-4 flex justify-between items-center",
                children: [
                    (0, jsx_runtime_1.jsxs)("div", {
                        children: [
                            (0, jsx_runtime_1.jsx)("h2", {
                                className: "text-lg font-semibold text-gray-900",
                                children: agent.agent_name
                            }),
                            (0, jsx_runtime_1.jsxs)("p", {
                                className: "text-sm text-gray-500",
                                children: [
                                    agent.model_provider,
                                    " \u2022 ",
                                    agent.model_name
                                ]
                            })
                        ]
                    }),
                    (0, jsx_runtime_1.jsxs)(Button_1.Button, {
                        variant: "secondary",
                        size: "sm",
                        onClick: onNewChat,
                        disabled: loading,
                        children: [
                            (0, jsx_runtime_1.jsx)(lucide_react_1.Plus, {
                                size: 16,
                                className: "mr-1"
                            }),
                            "New Chat"
                        ]
                    })
                ]
            }),
            (0, jsx_runtime_1.jsxs)("div", {
                className: "flex-1 overflow-y-auto p-6 space-y-4",
                children: [
                    messages.length === 0 && !streamingMessage && (0, jsx_runtime_1.jsxs)("div", {
                        className: "text-center text-gray-500 mt-8",
                        children: [
                            (0, jsx_runtime_1.jsxs)("p", {
                                className: "text-lg mb-2",
                                children: [
                                    "Start a conversation with ",
                                    agent.agent_name
                                ]
                            }),
                            (0, jsx_runtime_1.jsx)("p", {
                                className: "text-sm",
                                children: "Type a message below to begin"
                            })
                        ]
                    }),
                    messages.map((message)=>(0, jsx_runtime_1.jsx)(MessageBubble_1.MessageBubble, {
                            message: message
                        }, message.message_id)),
                    streamingMessage && (0, jsx_runtime_1.jsx)(MessageBubble_1.MessageBubble, {
                        message: {
                            message_id: 'streaming',
                            chat_id: currentChat?.chat_id || '',
                            sender: 'agent',
                            content: streamingMessage,
                            timestamp: new Date()
                        },
                        isStreaming: true
                    }),
                    (0, jsx_runtime_1.jsx)("div", {
                        ref: messagesEndRef
                    })
                ]
            }),
            (0, jsx_runtime_1.jsx)("div", {
                className: "border-t bg-white p-6",
                children: (0, jsx_runtime_1.jsxs)("form", {
                    onSubmit: handleSubmit,
                    className: "flex space-x-3",
                    children: [
                        (0, jsx_runtime_1.jsx)("div", {
                            className: "flex-1",
                            children: (0, jsx_runtime_1.jsx)("textarea", {
                                ref: textareaRef,
                                value: input,
                                onChange: (e)=>setInput(e.target.value),
                                onKeyDown: handleKeyDown,
                                placeholder: "Type your message...",
                                className: "w-full px-3 py-2 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 max-h-32",
                                rows: 1,
                                disabled: sending || loading
                            })
                        }),
                        (0, jsx_runtime_1.jsx)(Button_1.Button, {
                            type: "submit",
                            disabled: !input.trim() || sending || loading,
                            loading: sending,
                            children: (0, jsx_runtime_1.jsx)(lucide_react_1.Send, {
                                size: 16
                            })
                        })
                    ]
                })
            })
        ]
    });
};
exports.ChatInterface = ChatInterface; //# sourceMappingURL=ChatInterface.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/components/ChatThreadList.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ChatThreadList = void 0;
const jsx_runtime_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const Button_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/ui/Button.js [app-client] (ecmascript)");
const lucide_react_1 = __turbopack_context__.r("[project]/node_modules/lucide-react/dist/esm/lucide-react.js [app-client] (ecmascript)");
const ChatThreadList = ({ threads, currentThreadId, onSelectThread, onDeleteThread, loading = false })=>{
    if (loading) {
        return (0, jsx_runtime_1.jsx)("div", {
            className: "space-y-2",
            children: [
                ...Array(3)
            ].map((_, i)=>(0, jsx_runtime_1.jsx)("div", {
                    className: "animate-pulse",
                    children: (0, jsx_runtime_1.jsx)("div", {
                        className: "bg-gray-200 rounded h-16"
                    })
                }, i))
        });
    }
    if (threads.length === 0) {
        return (0, jsx_runtime_1.jsxs)("div", {
            className: "text-center py-8 text-gray-500",
            children: [
                (0, jsx_runtime_1.jsx)(lucide_react_1.MessageCircle, {
                    size: 48,
                    className: "mx-auto mb-3 text-gray-300"
                }),
                (0, jsx_runtime_1.jsx)("p", {
                    children: "No conversations yet"
                }),
                (0, jsx_runtime_1.jsx)("p", {
                    className: "text-sm",
                    children: "Start chatting to create conversation threads"
                })
            ]
        });
    }
    return (0, jsx_runtime_1.jsx)("div", {
        className: "space-y-2",
        children: threads.map((thread)=>(0, jsx_runtime_1.jsx)("div", {
                className: `group border rounded-lg p-3 cursor-pointer transition-colors ${currentThreadId === thread.chat_id ? 'bg-blue-50 border-blue-200' : 'bg-white hover:bg-gray-50'}`,
                onClick: ()=>onSelectThread(thread),
                children: (0, jsx_runtime_1.jsxs)("div", {
                    className: "flex justify-between items-start",
                    children: [
                        (0, jsx_runtime_1.jsxs)("div", {
                            className: "flex-1 min-w-0",
                            children: [
                                (0, jsx_runtime_1.jsx)("h4", {
                                    className: "font-medium text-gray-900 truncate",
                                    children: thread.title || 'Untitled Conversation'
                                }),
                                (0, jsx_runtime_1.jsxs)("p", {
                                    className: "text-sm text-gray-500 mt-1",
                                    children: [
                                        new Date(thread.last_updated_time).toLocaleDateString(),
                                        " at",
                                        ' ',
                                        new Date(thread.last_updated_time).toLocaleTimeString([], {
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        })
                                    ]
                                })
                            ]
                        }),
                        (0, jsx_runtime_1.jsx)(Button_1.Button, {
                            size: "sm",
                            variant: "ghost",
                            onClick: (e)=>{
                                e.stopPropagation();
                                onDeleteThread(thread.chat_id);
                            },
                            className: "opacity-0 group-hover:opacity-100 transition-opacity",
                            title: "Delete conversation",
                            children: (0, jsx_runtime_1.jsx)(lucide_react_1.Trash2, {
                                size: 14
                            })
                        })
                    ]
                })
            }, thread.chat_id))
    });
};
exports.ChatThreadList = ChatThreadList; //# sourceMappingURL=ChatThreadList.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/components/ui/Modal.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.Modal = void 0;
const jsx_runtime_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const lucide_react_1 = __turbopack_context__.r("[project]/node_modules/lucide-react/dist/esm/lucide-react.js [app-client] (ecmascript)");
const Modal = ({ isOpen, onClose, title, children, size = 'md' })=>{
    if (!isOpen) return null;
    const sizeClasses = {
        sm: 'max-w-md',
        md: 'max-w-lg',
        lg: 'max-w-2xl',
        xl: 'max-w-4xl'
    };
    return (0, jsx_runtime_1.jsx)("div", {
        className: "fixed inset-0 z-50 overflow-y-auto",
        children: (0, jsx_runtime_1.jsxs)("div", {
            className: "flex min-h-screen items-center justify-center p-4",
            children: [
                (0, jsx_runtime_1.jsx)("div", {
                    className: "fixed inset-0 bg-black bg-opacity-50 transition-opacity",
                    onClick: onClose
                }),
                (0, jsx_runtime_1.jsxs)("div", {
                    className: `relative bg-white rounded-lg shadow-xl w-full ${sizeClasses[size]}`,
                    children: [
                        title && (0, jsx_runtime_1.jsxs)("div", {
                            className: "flex items-center justify-between p-6 border-b",
                            children: [
                                (0, jsx_runtime_1.jsx)("h3", {
                                    className: "text-lg font-semibold text-gray-900",
                                    children: title
                                }),
                                (0, jsx_runtime_1.jsx)("button", {
                                    onClick: onClose,
                                    className: "text-gray-400 hover:text-gray-600 transition-colors",
                                    children: (0, jsx_runtime_1.jsx)(lucide_react_1.X, {
                                        size: 20
                                    })
                                })
                            ]
                        }),
                        (0, jsx_runtime_1.jsx)("div", {
                            className: "p-6",
                            children: children
                        })
                    ]
                })
            ]
        })
    });
};
exports.Modal = Modal; //# sourceMappingURL=Modal.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/hooks/useAgent.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.useAgent = useAgent;
const react_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
function useAgent(database, userId) {
    const [agents, setAgents] = (0, react_1.useState)([]);
    const [loading, setLoading] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)(null);
    const loadAgents = async ()=>{
        setLoading(true);
        setError(null);
        try {
            const agentList = await database.listAgents(userId);
            setAgents(agentList);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load agents');
        } finally{
            setLoading(false);
        }
    };
    const createAgent = async (data)=>{
        setError(null);
        try {
            const agent = await database.createAgent({
                ...data,
                user_id: userId
            });
            setAgents((prev)=>[
                    ...prev,
                    agent
                ]);
            return agent;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to create agent';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    };
    const updateAgent = async (agentId, data)=>{
        setError(null);
        try {
            const updatedAgent = await database.updateAgent(agentId, data, userId);
            setAgents((prev)=>prev.map((agent)=>agent.agent_id === agentId ? updatedAgent : agent));
            return updatedAgent;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to update agent';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    };
    const deleteAgent = async (agentId)=>{
        setError(null);
        try {
            await database.deleteAgent(agentId, userId);
            setAgents((prev)=>prev.filter((agent)=>agent.agent_id !== agentId));
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to delete agent';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    };
    const getAgent = async (agentId)=>{
        setError(null);
        try {
            return await database.getAgent(agentId, userId);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to get agent';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    };
    (0, react_1.useEffect)(()=>{
        loadAgents();
    }, [
        userId
    ]);
    return {
        agents,
        loading,
        error,
        createAgent,
        updateAgent,
        deleteAgent,
        getAgent,
        refreshAgents: loadAgents
    };
} //# sourceMappingURL=useAgent.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/utils/llm.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.LLMClient = exports.DEFAULT_PROVIDERS = void 0;
// Default LLM providers and models
exports.DEFAULT_PROVIDERS = [
    {
        name: 'OpenAI',
        requiresApiKey: true,
        models: [
            {
                id: 'gpt-4',
                name: 'GPT-4',
                description: 'Most capable model, best for complex tasks',
                maxTokens: 8192
            },
            {
                id: 'gpt-4-turbo-preview',
                name: 'GPT-4 Turbo',
                description: 'Latest GPT-4 model with improved performance',
                maxTokens: 128000
            },
            {
                id: 'gpt-3.5-turbo',
                name: 'GPT-3.5 Turbo',
                description: 'Fast and efficient for most tasks',
                maxTokens: 4096
            }
        ]
    },
    {
        name: 'OpenRouter',
        requiresApiKey: true,
        models: [
            {
                id: 'anthropic/claude-3-opus',
                name: 'Claude 3 Opus',
                description: 'Anthropic\'s most powerful model',
                maxTokens: 200000
            },
            {
                id: 'anthropic/claude-3-sonnet',
                name: 'Claude 3 Sonnet',
                description: 'Balanced performance and speed',
                maxTokens: 200000
            },
            {
                id: 'anthropic/claude-3-haiku',
                name: 'Claude 3 Haiku',
                description: 'Fast and efficient',
                maxTokens: 200000
            },
            {
                id: 'openai/gpt-4',
                name: 'GPT-4 (via OpenRouter)',
                description: 'OpenAI GPT-4 through OpenRouter',
                maxTokens: 8192
            },
            {
                id: 'meta-llama/llama-2-70b-chat',
                name: 'Llama 2 70B Chat',
                description: 'Meta\'s open-source model',
                maxTokens: 4096
            }
        ]
    }
];
class LLMClient {
    constructor(apiKey, provider){
        this.apiKey = apiKey;
        this.provider = provider;
        switch(provider){
            case 'OpenAI':
                this.baseUrl = 'https://api.openai.com/v1';
                break;
            case 'OpenRouter':
                this.baseUrl = 'https://openrouter.ai/api/v1';
                break;
            default:
                throw new Error(`Unsupported provider: ${provider}`);
        }
    }
    async createChatCompletion(request) {
        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
        };
        if (this.provider === 'OpenRouter') {
            headers['HTTP-Referer'] = 'https://agent-director.app';
            headers['X-Title'] = 'Agent Director';
        }
        const response = await fetch(`${this.baseUrl}/chat/completions`, {
            method: 'POST',
            headers,
            body: JSON.stringify(request)
        });
        if (!response.ok) {
            const error = await response.text();
            throw new Error(`LLM API error: ${response.status} ${error}`);
        }
        return response.json();
    }
    async *streamChatCompletion(request) {
        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`,
            'Accept': 'text/event-stream'
        };
        if (this.provider === 'OpenRouter') {
            headers['HTTP-Referer'] = 'https://agent-director.app';
            headers['X-Title'] = 'Agent Director';
        }
        const response = await fetch(`${this.baseUrl}/chat/completions`, {
            method: 'POST',
            headers,
            body: JSON.stringify({
                ...request,
                stream: true
            })
        });
        if (!response.ok) {
            const error = await response.text();
            throw new Error(`LLM API error: ${response.status} ${error}`);
        }
        const reader = response.body?.getReader();
        if (!reader) throw new Error('No response body');
        const decoder = new TextDecoder();
        try {
            while(true){
                const { done, value } = await reader.read();
                if (done) break;
                const chunk = decoder.decode(value, {
                    stream: true
                });
                const lines = chunk.split('\n');
                for (const line of lines){
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') return;
                        try {
                            const parsed = JSON.parse(data);
                            const content = parsed.choices?.[0]?.delta?.content;
                            if (content) {
                                yield content;
                            }
                        } catch (e) {
                        // Skip invalid JSON
                        }
                    }
                }
            }
        } finally{
            reader.releaseLock();
        }
    }
}
exports.LLMClient = LLMClient; //# sourceMappingURL=llm.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/hooks/useChat.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.useChat = useChat;
const react_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
const llm_1 = __turbopack_context__.r("[project]/packages/shared/dist/utils/llm.js [app-client] (ecmascript)");
function useChat(database, agent, userId) {
    const [threads, setThreads] = (0, react_1.useState)([]);
    const [currentThread, setCurrentThread] = (0, react_1.useState)(null);
    const [messages, setMessages] = (0, react_1.useState)([]);
    const [loading, setLoading] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)(null);
    const [streamingMessage, setStreamingMessage] = (0, react_1.useState)('');
    const [sending, setSending] = (0, react_1.useState)(false);
    const loadThreads = async ()=>{
        if (!agent) return;
        setLoading(true);
        setError(null);
        try {
            const threadList = await database.listChats(agent.agent_id, userId);
            setThreads(threadList);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load chat threads');
        } finally{
            setLoading(false);
        }
    };
    const loadMessages = async (threadId)=>{
        setError(null);
        try {
            const messageList = await database.getMessages(threadId, userId);
            setMessages(messageList);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load messages');
        }
    };
    const createNewThread = async ()=>{
        if (!agent) throw new Error('No agent selected');
        setError(null);
        try {
            const thread = await database.createChat({
                agent_id: agent.agent_id,
                user_id: userId,
                title: 'New Conversation'
            });
            setThreads((prev)=>[
                    thread,
                    ...prev
                ]);
            setCurrentThread(thread);
            setMessages([]);
            return thread;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to create chat thread';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    };
    const selectThread = async (thread)=>{
        setCurrentThread(thread);
        await loadMessages(thread.chat_id);
    };
    const deleteThread = async (threadId)=>{
        setError(null);
        try {
            await database.deleteChat(threadId, userId);
            setThreads((prev)=>prev.filter((t)=>t.chat_id !== threadId));
            if (currentThread?.chat_id === threadId) {
                setCurrentThread(null);
                setMessages([]);
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to delete chat thread';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    };
    const sendMessage = async (content)=>{
        if (!agent || !currentThread) {
            throw new Error('No agent or chat thread selected');
        }
        setSending(true);
        setError(null);
        setStreamingMessage('');
        try {
            // Add user message
            const userMessage = await database.addMessage({
                chat_id: currentThread.chat_id,
                sender: 'user',
                content
            });
            setMessages((prev)=>[
                    ...prev,
                    userMessage
                ]);
            // Prepare messages for LLM
            const conversationMessages = [
                {
                    role: 'system',
                    content: agent.system_instructions
                },
                ...messages.map((msg)=>({
                        role: msg.sender === 'user' ? 'user' : 'assistant',
                        content: msg.content
                    })),
                {
                    role: 'user',
                    content
                }
            ];
            // Get API key (decrypt if needed)
            const apiKey = agent.api_key;
            if (!apiKey) {
                throw new Error('No API key configured for this agent');
            }
            const llmClient = new llm_1.LLMClient(apiKey, agent.model_provider);
            // Stream response
            let fullResponse = '';
            for await (const chunk of llmClient.streamChatCompletion({
                model: agent.model_name,
                messages: conversationMessages,
                stream: true
            })){
                fullResponse += chunk;
                setStreamingMessage(fullResponse);
            }
            // Save agent response
            const agentMessage = await database.addMessage({
                chat_id: currentThread.chat_id,
                sender: 'agent',
                content: fullResponse
            });
            setMessages((prev)=>[
                    ...prev,
                    agentMessage
                ]);
            setStreamingMessage('');
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
            setError(errorMessage);
            setStreamingMessage('');
            throw new Error(errorMessage);
        } finally{
            setSending(false);
        }
    };
    (0, react_1.useEffect)(()=>{
        if (agent) {
            loadThreads();
        } else {
            setThreads([]);
            setCurrentThread(null);
            setMessages([]);
        }
    }, [
        agent?.agent_id,
        userId
    ]);
    return {
        threads,
        currentThread,
        messages,
        loading,
        error,
        streamingMessage,
        sending,
        createNewThread,
        selectThread,
        deleteThread,
        sendMessage,
        refreshThreads: loadThreads
    };
} //# sourceMappingURL=useChat.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/hooks/useLLMProviders.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.useLLMProviders = useLLMProviders;
const react_1 = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
const llm_1 = __turbopack_context__.r("[project]/packages/shared/dist/utils/llm.js [app-client] (ecmascript)");
function useLLMProviders() {
    const [providers, setProviders] = (0, react_1.useState)(llm_1.DEFAULT_PROVIDERS);
    const [loading, setLoading] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)(null);
    // In the future, this could fetch providers from an API
    const loadProviders = async ()=>{
        setLoading(true);
        setError(null);
        try {
            // For now, just use the default providers
            // In the future, this could fetch from an API to get updated model lists
            setProviders(llm_1.DEFAULT_PROVIDERS);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load providers');
        } finally{
            setLoading(false);
        }
    };
    const getProvider = (name)=>{
        return providers.find((p)=>p.name === name);
    };
    const getModel = (providerName, modelId)=>{
        const provider = getProvider(providerName);
        return provider?.models.find((m)=>m.id === modelId);
    };
    (0, react_1.useEffect)(()=>{
        loadProviders();
    }, []);
    return {
        providers,
        loading,
        error,
        getProvider,
        getModel,
        refreshProviders: loadProviders
    };
} //# sourceMappingURL=useLLMProviders.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/utils/api.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ApiClient = void 0;
exports.streamResponse = streamResponse;
exports.createEventSource = createEventSource;
class ApiClient {
    constructor(baseUrl = ''){
        this.baseUrl = baseUrl;
    }
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const defaultHeaders = {
            'Content-Type': 'application/json'
        };
        const config = {
            ...options,
            headers: {
                ...defaultHeaders,
                ...options.headers
            }
        };
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            if (!response.ok) {
                return {
                    success: false,
                    error: data.error || `HTTP ${response.status}: ${response.statusText}`
                };
            }
            return {
                success: true,
                data
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Network error'
            };
        }
    }
    async get(endpoint, headers) {
        return this.request(endpoint, {
            method: 'GET',
            headers
        });
    }
    async post(endpoint, data, headers) {
        return this.request(endpoint, {
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined,
            headers
        });
    }
    async put(endpoint, data, headers) {
        return this.request(endpoint, {
            method: 'PUT',
            body: data ? JSON.stringify(data) : undefined,
            headers
        });
    }
    async delete(endpoint, headers) {
        return this.request(endpoint, {
            method: 'DELETE',
            headers
        });
    }
}
exports.ApiClient = ApiClient;
// Streaming utilities
async function* streamResponse(response) {
    const reader = response.body?.getReader();
    if (!reader) throw new Error('No response body');
    const decoder = new TextDecoder();
    try {
        while(true){
            const { done, value } = await reader.read();
            if (done) break;
            const chunk = decoder.decode(value, {
                stream: true
            });
            const lines = chunk.split('\n');
            for (const line of lines){
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);
                    if (data === '[DONE]') return;
                    try {
                        const parsed = JSON.parse(data);
                        if (parsed.content) {
                            yield parsed.content;
                        }
                    } catch (e) {
                    // Skip invalid JSON
                    }
                }
            }
        }
    } finally{
        reader.releaseLock();
    }
}
function createEventSource(url, onMessage, onError) {
    const eventSource = new EventSource(url);
    eventSource.onmessage = (event)=>{
        if (event.data === '[DONE]') {
            eventSource.close();
            return;
        }
        try {
            const parsed = JSON.parse(event.data);
            if (parsed.content) {
                onMessage(parsed.content);
            }
        } catch (e) {
        // Skip invalid JSON
        }
    };
    if (onError) {
        eventSource.onerror = onError;
    }
    return eventSource;
} //# sourceMappingURL=api.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/utils/storage.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ElectronStoreAdapter = exports.LocalStorageAdapter = void 0;
exports.encryptApiKey = encryptApiKey;
exports.decryptApiKey = decryptApiKey;
// Web localStorage adapter
class LocalStorageAdapter {
    async getItem(key) {
        return localStorage.getItem(key);
    }
    async setItem(key, value) {
        localStorage.setItem(key, value);
    }
    async removeItem(key) {
        localStorage.removeItem(key);
    }
    async clear() {
        localStorage.clear();
    }
}
exports.LocalStorageAdapter = LocalStorageAdapter;
// Electron store adapter (for desktop)
class ElectronStoreAdapter {
    constructor(store){
        this.store = store;
    }
    async getItem(key) {
        return this.store.get(key) || null;
    }
    async setItem(key, value) {
        this.store.set(key, value);
    }
    async removeItem(key) {
        this.store.delete(key);
    }
    async clear() {
        this.store.clear();
    }
}
exports.ElectronStoreAdapter = ElectronStoreAdapter;
// Utility functions
async function encryptApiKey(apiKey, secretKey) {
    // Simple encryption for demo - in production, use proper encryption
    const encoder = new TextEncoder();
    const data = encoder.encode(apiKey);
    const key = await crypto.subtle.importKey('raw', encoder.encode(secretKey.padEnd(32, '0').slice(0, 32)), {
        name: 'AES-GCM'
    }, false, [
        'encrypt'
    ]);
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const encrypted = await crypto.subtle.encrypt({
        name: 'AES-GCM',
        iv
    }, key, data);
    const result = new Uint8Array(iv.length + encrypted.byteLength);
    result.set(iv);
    result.set(new Uint8Array(encrypted), iv.length);
    return btoa(String.fromCharCode(...result));
}
async function decryptApiKey(encryptedApiKey, secretKey) {
    try {
        const data = new Uint8Array(atob(encryptedApiKey).split('').map((c)=>c.charCodeAt(0)));
        const iv = data.slice(0, 12);
        const encrypted = data.slice(12);
        const encoder = new TextEncoder();
        const key = await crypto.subtle.importKey('raw', encoder.encode(secretKey.padEnd(32, '0').slice(0, 32)), {
            name: 'AES-GCM'
        }, false, [
            'decrypt'
        ]);
        const decrypted = await crypto.subtle.decrypt({
            name: 'AES-GCM',
            iv
        }, key, encrypted);
        return new TextDecoder().decode(decrypted);
    } catch (error) {
        throw new Error('Failed to decrypt API key');
    }
} //# sourceMappingURL=storage.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/shared/dist/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = {
            enumerable: true,
            get: function() {
                return m[k];
            }
        };
    }
    Object.defineProperty(o, k2, desc);
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __exportStar = this && this.__exportStar || function(m, exports1) {
    for(var p in m)if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.useLLMProviders = exports.useChat = exports.useAgent = exports.Modal = exports.Select = exports.TextArea = exports.Input = exports.Button = exports.MessageBubble = exports.ChatThreadList = exports.ChatInterface = exports.AgentList = exports.AgentForm = void 0;
// Export all shared components
var AgentForm_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/AgentForm.js [app-client] (ecmascript)");
Object.defineProperty(exports, "AgentForm", {
    enumerable: true,
    get: function() {
        return AgentForm_1.AgentForm;
    }
});
var AgentList_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/AgentList.js [app-client] (ecmascript)");
Object.defineProperty(exports, "AgentList", {
    enumerable: true,
    get: function() {
        return AgentList_1.AgentList;
    }
});
var ChatInterface_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/ChatInterface.js [app-client] (ecmascript)");
Object.defineProperty(exports, "ChatInterface", {
    enumerable: true,
    get: function() {
        return ChatInterface_1.ChatInterface;
    }
});
var ChatThreadList_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/ChatThreadList.js [app-client] (ecmascript)");
Object.defineProperty(exports, "ChatThreadList", {
    enumerable: true,
    get: function() {
        return ChatThreadList_1.ChatThreadList;
    }
});
var MessageBubble_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/MessageBubble.js [app-client] (ecmascript)");
Object.defineProperty(exports, "MessageBubble", {
    enumerable: true,
    get: function() {
        return MessageBubble_1.MessageBubble;
    }
});
var Button_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/ui/Button.js [app-client] (ecmascript)");
Object.defineProperty(exports, "Button", {
    enumerable: true,
    get: function() {
        return Button_1.Button;
    }
});
var Input_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/ui/Input.js [app-client] (ecmascript)");
Object.defineProperty(exports, "Input", {
    enumerable: true,
    get: function() {
        return Input_1.Input;
    }
});
var TextArea_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/ui/TextArea.js [app-client] (ecmascript)");
Object.defineProperty(exports, "TextArea", {
    enumerable: true,
    get: function() {
        return TextArea_1.TextArea;
    }
});
var Select_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/ui/Select.js [app-client] (ecmascript)");
Object.defineProperty(exports, "Select", {
    enumerable: true,
    get: function() {
        return Select_1.Select;
    }
});
var Modal_1 = __turbopack_context__.r("[project]/packages/shared/dist/components/ui/Modal.js [app-client] (ecmascript)");
Object.defineProperty(exports, "Modal", {
    enumerable: true,
    get: function() {
        return Modal_1.Modal;
    }
});
// Export hooks
var useAgent_1 = __turbopack_context__.r("[project]/packages/shared/dist/hooks/useAgent.js [app-client] (ecmascript)");
Object.defineProperty(exports, "useAgent", {
    enumerable: true,
    get: function() {
        return useAgent_1.useAgent;
    }
});
var useChat_1 = __turbopack_context__.r("[project]/packages/shared/dist/hooks/useChat.js [app-client] (ecmascript)");
Object.defineProperty(exports, "useChat", {
    enumerable: true,
    get: function() {
        return useChat_1.useChat;
    }
});
var useLLMProviders_1 = __turbopack_context__.r("[project]/packages/shared/dist/hooks/useLLMProviders.js [app-client] (ecmascript)");
Object.defineProperty(exports, "useLLMProviders", {
    enumerable: true,
    get: function() {
        return useLLMProviders_1.useLLMProviders;
    }
});
// Export utilities
__exportStar(__turbopack_context__.r("[project]/packages/shared/dist/utils/api.js [app-client] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/packages/shared/dist/utils/storage.js [app-client] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/packages/shared/dist/utils/llm.js [app-client] (ecmascript)"), exports); //# sourceMappingURL=index.js.map
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/web/src/components/LoginPage.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "LoginPage": (()=>LoginPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/web/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/shared/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bot.js [app-client] (ecmascript) <export default as Bot>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$share$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Share2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/share-2.js [app-client] (ecmascript) <export default as Share2>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function LoginPage() {
    _s();
    const { signInWithGoogle } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const handleSignIn = async ()=>{
        setLoading(true);
        try {
            await signInWithGoogle();
        } catch (error) {
            console.error('Sign in error:', error);
        // TODO: Show error toast
        } finally{
            setLoading(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container mx-auto px-4 py-16",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center mb-16",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-center mb-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__["Bot"], {
                                    size: 48,
                                    className: "text-blue-600 mr-3"
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                    lineNumber: 30,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: "text-4xl font-bold text-gray-900",
                                    children: "Agent Director"
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                    lineNumber: 31,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                            lineNumber: 29,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-xl text-gray-600 max-w-2xl mx-auto",
                            children: "Create, manage, and share AI agents with custom personalities and behaviors. Build your own AI workforce and share them with the world."
                        }, void 0, false, {
                            fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                            lineNumber: 33,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                    lineNumber: 28,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid md:grid-cols-3 gap-8 mb-16",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-center p-6 bg-white rounded-lg shadow-sm",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__["Bot"], {
                                    size: 32,
                                    className: "text-blue-600 mx-auto mb-4"
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                    lineNumber: 42,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-semibold mb-2",
                                    children: "Create Custom Agents"
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                    lineNumber: 43,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-gray-600",
                                    children: "Define AI agents with custom system instructions and choose from multiple LLM providers"
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                    lineNumber: 44,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                            lineNumber: 41,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-center p-6 bg-white rounded-lg shadow-sm",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                                    size: 32,
                                    className: "text-green-600 mx-auto mb-4"
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                    lineNumber: 50,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-semibold mb-2",
                                    children: "Chat & Collaborate"
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                    lineNumber: 51,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-gray-600",
                                    children: "Have conversations with your agents and manage multiple chat threads"
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                    lineNumber: 52,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                            lineNumber: 49,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-center p-6 bg-white rounded-lg shadow-sm",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$share$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Share2$3e$__["Share2"], {
                                    size: 32,
                                    className: "text-purple-600 mx-auto mb-4"
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                    lineNumber: 58,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-semibold mb-2",
                                    children: "Share & Discover"
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                    lineNumber: 59,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-gray-600",
                                    children: "Share your agents with others or clone interesting agents from the community"
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                    lineNumber: 60,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                            lineNumber: 57,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                    lineNumber: 40,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-md mx-auto bg-white rounded-lg shadow-lg p-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-center mb-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-2xl font-bold text-gray-900 mb-2",
                                    children: "Get Started"
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                    lineNumber: 69,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-gray-600",
                                    children: "Sign in to create and manage your AI agents"
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                    lineNumber: 70,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                            lineNumber: 68,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            onClick: handleSignIn,
                            loading: loading,
                            className: "w-full flex items-center justify-center",
                            size: "lg",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    className: "w-5 h-5 mr-3",
                                    viewBox: "0 0 24 24",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            fill: "currentColor",
                                            d: "M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                                        }, void 0, false, {
                                            fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                            lineNumber: 80,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            fill: "currentColor",
                                            d: "M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                                        }, void 0, false, {
                                            fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                            lineNumber: 84,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            fill: "currentColor",
                                            d: "M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                                        }, void 0, false, {
                                            fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                            lineNumber: 88,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            fill: "currentColor",
                                            d: "M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                                        }, void 0, false, {
                                            fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                            lineNumber: 92,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                                    lineNumber: 79,
                                    columnNumber: 13
                                }, this),
                                "Continue with Google"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                            lineNumber: 73,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-xs text-gray-500 text-center mt-4",
                            children: "By signing in, you agree to our Terms of Service and Privacy Policy"
                        }, void 0, false, {
                            fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                            lineNumber: 100,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                    lineNumber: 67,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center mt-16 text-gray-500",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "© 2024 Agent Director. Built with ❤️ for the AI community."
                    }, void 0, false, {
                        fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                        lineNumber: 107,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/packages/web/src/components/LoginPage.tsx",
                    lineNumber: 106,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/packages/web/src/components/LoginPage.tsx",
            lineNumber: 26,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/packages/web/src/components/LoginPage.tsx",
        lineNumber: 25,
        columnNumber: 5
    }, this);
}
_s(LoginPage, "LCRTzPT4aZS2lmYjpQB7jtACTT8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = LoginPage;
var _c;
__turbopack_context__.k.register(_c, "LoginPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/web/src/lib/database.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SupabaseDatabaseAdapter": (()=>SupabaseDatabaseAdapter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/web/src/lib/supabase.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-browser/v4.js [app-client] (ecmascript) <export default as v4>");
;
;
class SupabaseDatabaseAdapter {
    async createAgent(agent) {
        const now = new Date().toISOString();
        const agentData = {
            agent_id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
            ...agent,
            creation_date: now,
            last_modified_date: now
        };
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('agents').insert(agentData).select().single();
        if (error) throw new Error(`Failed to create agent: ${error.message}`);
        return {
            ...data,
            creation_date: new Date(data.creation_date),
            last_modified_date: new Date(data.last_modified_date)
        };
    }
    async getAgent(agentId, userId) {
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('agents').select('*').eq('agent_id', agentId);
        if (userId) {
            query = query.eq('user_id', userId);
        }
        const { data, error } = await query.single();
        if (error) {
            if (error.code === 'PGRST116') return null; // Not found
            throw new Error(`Failed to get agent: ${error.message}`);
        }
        return {
            ...data,
            creation_date: new Date(data.creation_date),
            last_modified_date: new Date(data.last_modified_date)
        };
    }
    async listAgents(userId) {
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('agents').select('*').order('last_modified_date', {
            ascending: false
        });
        if (userId) {
            query = query.eq('user_id', userId);
        }
        const { data, error } = await query;
        if (error) throw new Error(`Failed to list agents: ${error.message}`);
        return data.map((agent)=>({
                ...agent,
                creation_date: new Date(agent.creation_date),
                last_modified_date: new Date(agent.last_modified_date)
            }));
    }
    async updateAgent(agentId, updates, userId) {
        const updateData = {
            ...updates,
            last_modified_date: new Date().toISOString()
        };
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('agents').update(updateData).eq('agent_id', agentId);
        if (userId) {
            query = query.eq('user_id', userId);
        }
        const { data, error } = await query.select().single();
        if (error) throw new Error(`Failed to update agent: ${error.message}`);
        return {
            ...data,
            creation_date: new Date(data.creation_date),
            last_modified_date: new Date(data.last_modified_date)
        };
    }
    async deleteAgent(agentId, userId) {
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('agents').delete().eq('agent_id', agentId);
        if (userId) {
            query = query.eq('user_id', userId);
        }
        const { error } = await query;
        if (error) throw new Error(`Failed to delete agent: ${error.message}`);
        return true;
    }
    async createChat(chat) {
        const now = new Date().toISOString();
        const chatData = {
            chat_id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
            ...chat,
            start_time: now,
            last_updated_time: now
        };
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('chat_threads').insert(chatData).select().single();
        if (error) throw new Error(`Failed to create chat: ${error.message}`);
        return {
            ...data,
            start_time: new Date(data.start_time),
            last_updated_time: new Date(data.last_updated_time)
        };
    }
    async getChat(chatId, userId) {
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('chat_threads').select('*').eq('chat_id', chatId);
        if (userId) {
            query = query.eq('user_id', userId);
        }
        const { data, error } = await query.single();
        if (error) {
            if (error.code === 'PGRST116') return null; // Not found
            throw new Error(`Failed to get chat: ${error.message}`);
        }
        return {
            ...data,
            start_time: new Date(data.start_time),
            last_updated_time: new Date(data.last_updated_time)
        };
    }
    async listChats(agentId, userId) {
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('chat_threads').select('*').eq('agent_id', agentId).order('last_updated_time', {
            ascending: false
        });
        if (userId) {
            query = query.eq('user_id', userId);
        }
        const { data, error } = await query;
        if (error) throw new Error(`Failed to list chats: ${error.message}`);
        return data.map((chat)=>({
                ...chat,
                start_time: new Date(chat.start_time),
                last_updated_time: new Date(chat.last_updated_time)
            }));
    }
    async deleteChat(chatId, userId) {
        // First delete all messages in the chat
        await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('messages').delete().eq('chat_id', chatId);
        // Then delete the chat thread
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('chat_threads').delete().eq('chat_id', chatId);
        if (userId) {
            query = query.eq('user_id', userId);
        }
        const { error } = await query;
        if (error) throw new Error(`Failed to delete chat: ${error.message}`);
        return true;
    }
    async addMessage(message) {
        const messageData = {
            message_id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
            ...message,
            timestamp: new Date().toISOString()
        };
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('messages').insert(messageData).select().single();
        if (error) throw new Error(`Failed to add message: ${error.message}`);
        // Update chat thread's last_updated_time
        await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('chat_threads').update({
            last_updated_time: messageData.timestamp
        }).eq('chat_id', message.chat_id);
        return {
            ...data,
            timestamp: new Date(data.timestamp)
        };
    }
    async getMessages(chatId, userId) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('messages').select('*').eq('chat_id', chatId).order('timestamp', {
            ascending: true
        });
        if (error) throw new Error(`Failed to get messages: ${error.message}`);
        return data.map((message)=>({
                ...message,
                timestamp: new Date(message.timestamp)
            }));
    }
    async deleteMessages(chatId, userId) {
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('messages').delete().eq('chat_id', chatId);
        if (error) throw new Error(`Failed to delete messages: ${error.message}`);
        return true;
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/web/src/components/Dashboard.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Dashboard": (()=>Dashboard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/web/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/web/src/lib/database.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/shared/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/log-out.js [app-client] (ecmascript) <export default as LogOut>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bot.js [app-client] (ecmascript) <export default as Bot>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/message-circle.js [app-client] (ecmascript) <export default as MessageCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-client] (ecmascript) <export default as Settings>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
const database = new __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SupabaseDatabaseAdapter"]();
function Dashboard() {
    _s();
    const { user, signOut } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const { providers } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLLMProviders"])();
    const { agents, createAgent, updateAgent, deleteAgent, loading: agentsLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAgent"])(database, user?.id);
    const [selectedAgent, setSelectedAgent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const { threads, currentThread, messages, streamingMessage, createNewThread, selectThread, deleteThread, sendMessage, loading: chatLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useChat"])(database, selectedAgent, user?.id);
    const [showAgentForm, setShowAgentForm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [editingAgent, setEditingAgent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('agents');
    const handleCreateAgent = async (data)=>{
        try {
            const agent = await createAgent(data);
            setShowAgentForm(false);
            setSelectedAgent(agent);
            setActiveTab('chat');
        } catch (error) {
            console.error('Failed to create agent:', error);
        }
    };
    const handleUpdateAgent = async (data)=>{
        if (!editingAgent) return;
        try {
            await updateAgent(editingAgent.agent_id, data);
            setEditingAgent(null);
            setShowAgentForm(false);
        } catch (error) {
            console.error('Failed to update agent:', error);
        }
    };
    const handleDeleteAgent = async (agentId)=>{
        if (!confirm('Are you sure you want to delete this agent?')) return;
        try {
            await deleteAgent(agentId);
            if (selectedAgent?.agent_id === agentId) {
                setSelectedAgent(null);
                setActiveTab('agents');
            }
        } catch (error) {
            console.error('Failed to delete agent:', error);
        }
    };
    const handleChatWithAgent = async (agent)=>{
        setSelectedAgent(agent);
        setActiveTab('chat');
        // Create new thread if none exists
        if (threads.length === 0) {
            await createNewThread();
        }
    };
    const handleSendMessage = async (content)=>{
        if (!currentThread) {
            await createNewThread();
        }
        await sendMessage(content);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "bg-white border-b border-gray-200",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between items-center h-16",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__["Bot"], {
                                        size: 32,
                                        className: "text-blue-600 mr-3"
                                    }, void 0, false, {
                                        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                        lineNumber: 105,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "text-xl font-semibold text-gray-900",
                                        children: "Agent Director"
                                    }, void 0, false, {
                                        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                        lineNumber: 106,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                lineNumber: 104,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-sm text-gray-600",
                                        children: [
                                            "Welcome, ",
                                            user?.user_metadata?.full_name || user?.email
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                        lineNumber: 110,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "ghost",
                                        onClick: signOut,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__["LogOut"], {
                                                size: 16,
                                                className: "mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                lineNumber: 114,
                                                columnNumber: 17
                                            }, this),
                                            "Sign Out"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                        lineNumber: 113,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                lineNumber: 109,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                        lineNumber: 103,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                    lineNumber: 102,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                lineNumber: 101,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 lg:grid-cols-4 gap-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "lg:col-span-1",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-lg shadow-sm border border-gray-200",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "border-b border-gray-200",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                                            className: "flex",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>setActiveTab('agents'),
                                                    className: `flex-1 py-3 px-4 text-sm font-medium ${activeTab === 'agents' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                                                            size: 16,
                                                            className: "inline mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                            lineNumber: 138,
                                                            columnNumber: 21
                                                        }, this),
                                                        "Agents"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                    lineNumber: 130,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>setActiveTab('chat'),
                                                    className: `flex-1 py-3 px-4 text-sm font-medium ${activeTab === 'chat' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`,
                                                    disabled: !selectedAgent,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircle$3e$__["MessageCircle"], {
                                                            size: 16,
                                                            className: "inline mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                            lineNumber: 150,
                                                            columnNumber: 21
                                                        }, this),
                                                        "Chat"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                    lineNumber: 141,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                            lineNumber: 129,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                        lineNumber: 128,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-4",
                                        children: [
                                            activeTab === 'agents' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between items-center mb-4",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                                className: "text-lg font-semibold",
                                                                children: "Your Agents"
                                                            }, void 0, false, {
                                                                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                                lineNumber: 161,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                size: "sm",
                                                                onClick: ()=>setShowAgentForm(true),
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                                                        size: 16,
                                                                        className: "mr-1"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                                        lineNumber: 166,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    "New"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                                lineNumber: 162,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                        lineNumber: 160,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AgentList"], {
                                                        agents: agents,
                                                        onEdit: (agent)=>{
                                                            setEditingAgent(agent);
                                                            setShowAgentForm(true);
                                                        },
                                                        onDelete: handleDeleteAgent,
                                                        onChat: handleChatWithAgent,
                                                        loading: agentsLoading
                                                    }, void 0, false, {
                                                        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                        lineNumber: 171,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                lineNumber: 159,
                                                columnNumber: 19
                                            }, this),
                                            activeTab === 'chat' && selectedAgent && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "mb-4",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                                className: "text-lg font-semibold mb-2",
                                                                children: "Conversations"
                                                            }, void 0, false, {
                                                                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                                lineNumber: 187,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-sm text-gray-600 mb-4",
                                                                children: [
                                                                    "Chatting with ",
                                                                    selectedAgent.agent_name
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                                lineNumber: 188,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                        lineNumber: 186,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChatThreadList"], {
                                                        threads: threads,
                                                        currentThreadId: currentThread?.chat_id,
                                                        onSelectThread: selectThread,
                                                        onDeleteThread: deleteThread,
                                                        loading: chatLoading
                                                    }, void 0, false, {
                                                        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                        lineNumber: 193,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                lineNumber: 185,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                        lineNumber: 157,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                lineNumber: 126,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                            lineNumber: 125,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "lg:col-span-3",
                            children: [
                                activeTab === 'agents' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-2xl font-bold mb-6",
                                            children: "Agent Management"
                                        }, void 0, false, {
                                            fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                            lineNumber: 210,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-gray-600 mb-8",
                                            children: "Create and manage your AI agents. Each agent can have its own personality, knowledge, and behavior defined through system instructions."
                                        }, void 0, false, {
                                            fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                            lineNumber: 211,
                                            columnNumber: 17
                                        }, this),
                                        agents.length === 0 && !agentsLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-center py-12",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__["Bot"], {
                                                    size: 64,
                                                    className: "mx-auto text-gray-300 mb-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                    lineNumber: 218,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    className: "text-lg font-semibold text-gray-900 mb-2",
                                                    children: "No agents yet"
                                                }, void 0, false, {
                                                    fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                    lineNumber: 219,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-gray-600 mb-6",
                                                    children: "Create your first AI agent to get started"
                                                }, void 0, false, {
                                                    fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                    lineNumber: 222,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                    onClick: ()=>setShowAgentForm(true),
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                                            size: 16,
                                                            className: "mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                            lineNumber: 226,
                                                            columnNumber: 23
                                                        }, this),
                                                        "Create Your First Agent"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                    lineNumber: 225,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                            lineNumber: 217,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                    lineNumber: 209,
                                    columnNumber: 15
                                }, this),
                                activeTab === 'chat' && selectedAgent && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white rounded-lg shadow-sm border border-gray-200 h-[600px]",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChatInterface"], {
                                        agent: selectedAgent,
                                        currentChat: currentThread,
                                        messages: messages,
                                        onSendMessage: handleSendMessage,
                                        onNewChat: createNewThread,
                                        loading: chatLoading,
                                        streamingMessage: streamingMessage
                                    }, void 0, false, {
                                        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                        lineNumber: 236,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                    lineNumber: 235,
                                    columnNumber: 15
                                }, this),
                                activeTab === 'chat' && !selectedAgent && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-center py-12",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircle$3e$__["MessageCircle"], {
                                                size: 64,
                                                className: "mx-auto text-gray-300 mb-4"
                                            }, void 0, false, {
                                                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                lineNumber: 251,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "text-lg font-semibold text-gray-900 mb-2",
                                                children: "Select an agent to chat"
                                            }, void 0, false, {
                                                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                lineNumber: 252,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-gray-600",
                                                children: "Choose an agent from the sidebar to start a conversation"
                                            }, void 0, false, {
                                                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                                lineNumber: 255,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                        lineNumber: 250,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                                    lineNumber: 249,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                            lineNumber: 207,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                    lineNumber: 123,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                lineNumber: 122,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Modal"], {
                isOpen: showAgentForm,
                onClose: ()=>{
                    setShowAgentForm(false);
                    setEditingAgent(null);
                },
                title: editingAgent ? 'Edit Agent' : 'Create New Agent',
                size: "lg",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AgentForm"], {
                    agent: editingAgent || undefined,
                    providers: providers,
                    onSubmit: editingAgent ? handleUpdateAgent : handleCreateAgent,
                    onCancel: ()=>{
                        setShowAgentForm(false);
                        setEditingAgent(null);
                    }
                }, void 0, false, {
                    fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                    lineNumber: 275,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/packages/web/src/components/Dashboard.tsx",
                lineNumber: 266,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/packages/web/src/components/Dashboard.tsx",
        lineNumber: 99,
        columnNumber: 5
    }, this);
}
_s(Dashboard, "5HfgpHnTXs6OBl2xG+mu0zLjmyI=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLLMProviders"],
        __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAgent"],
        __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$shared$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useChat"]
    ];
});
_c = Dashboard;
var _c;
__turbopack_context__.k.register(_c, "Dashboard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/packages/web/src/app/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Home)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/web/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$components$2f$LoginPage$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/web/src/components/LoginPage.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$components$2f$Dashboard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/web/src/components/Dashboard.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function Home() {
    _s();
    const { user, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"
            }, void 0, false, {
                fileName: "[project]/packages/web/src/app/page.tsx",
                lineNumber: 13,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/packages/web/src/app/page.tsx",
            lineNumber: 12,
            columnNumber: 7
        }, this);
    }
    if (!user) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$components$2f$LoginPage$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LoginPage"], {}, void 0, false, {
            fileName: "[project]/packages/web/src/app/page.tsx",
            lineNumber: 19,
            columnNumber: 12
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$components$2f$Dashboard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Dashboard"], {}, void 0, false, {
        fileName: "[project]/packages/web/src/app/page.tsx",
        lineNumber: 22,
        columnNumber: 10
    }, this);
}
_s(Home, "EmJkapf7qiLC5Br5eCoEq4veZes=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$web$2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = Home;
var _c;
__turbopack_context__.k.register(_c, "Home");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=packages_c910e68d._.js.map