{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/agentdirector/packages/web/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Database schema types for Supabase\nexport interface Database {\n  public: {\n    Tables: {\n      agents: {\n        Row: {\n          agent_id: string;\n          user_id: string;\n          agent_name: string;\n          model_provider: string;\n          model_name: string;\n          api_key: string | null;\n          system_instructions: string;\n          creation_date: string;\n          last_modified_date: string;\n        };\n        Insert: {\n          agent_id?: string;\n          user_id: string;\n          agent_name: string;\n          model_provider: string;\n          model_name: string;\n          api_key?: string | null;\n          system_instructions: string;\n          creation_date?: string;\n          last_modified_date?: string;\n        };\n        Update: {\n          agent_id?: string;\n          user_id?: string;\n          agent_name?: string;\n          model_provider?: string;\n          model_name?: string;\n          api_key?: string | null;\n          system_instructions?: string;\n          creation_date?: string;\n          last_modified_date?: string;\n        };\n      };\n      chat_threads: {\n        Row: {\n          chat_id: string;\n          agent_id: string;\n          user_id: string;\n          start_time: string;\n          last_updated_time: string;\n          title: string | null;\n        };\n        Insert: {\n          chat_id?: string;\n          agent_id: string;\n          user_id: string;\n          start_time?: string;\n          last_updated_time?: string;\n          title?: string | null;\n        };\n        Update: {\n          chat_id?: string;\n          agent_id?: string;\n          user_id?: string;\n          start_time?: string;\n          last_updated_time?: string;\n          title?: string | null;\n        };\n      };\n      messages: {\n        Row: {\n          message_id: string;\n          chat_id: string;\n          sender: string;\n          content: string;\n          timestamp: string;\n        };\n        Insert: {\n          message_id?: string;\n          chat_id: string;\n          sender: string;\n          content: string;\n          timestamp?: string;\n        };\n        Update: {\n          message_id?: string;\n          chat_id?: string;\n          sender?: string;\n          content?: string;\n          timestamp?: string;\n        };\n      };\n      shared_agent_links: {\n        Row: {\n          link_id: string;\n          agent_id: string;\n          author_user_id: string;\n          share_mode: string;\n          creation_date: string;\n        };\n        Insert: {\n          link_id?: string;\n          agent_id: string;\n          author_user_id: string;\n          share_mode: string;\n          creation_date?: string;\n        };\n        Update: {\n          link_id?: string;\n          agent_id?: string;\n          author_user_id?: string;\n          share_mode?: string;\n          creation_date?: string;\n        };\n      };\n      users: {\n        Row: {\n          user_id: string;\n          email: string;\n          name: string;\n          avatar_url: string | null;\n          created_at: string;\n        };\n        Insert: {\n          user_id: string;\n          email: string;\n          name: string;\n          avatar_url?: string | null;\n          created_at?: string;\n        };\n        Update: {\n          user_id?: string;\n          email?: string;\n          name?: string;\n          avatar_url?: string | null;\n          created_at?: string;\n        };\n      };\n    };\n  };\n}\n"], "names": [], "mappings": ";;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/agentdirector/packages/web/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User } from '@supabase/supabase-js';\nimport { supabase } from '@/lib/supabase';\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  signInWithGoogle: () => Promise<void>;\n  signOut: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Get initial session\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setUser(session?.user ?? null);\n      setLoading(false);\n    });\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser(session?.user ?? null);\n      setLoading(false);\n    });\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  const signInWithGoogle = async () => {\n    const { error } = await supabase.auth.signInWithOAuth({\n      provider: 'google',\n      options: {\n        redirectTo: `${window.location.origin}/auth/callback`,\n      },\n    });\n\n    if (error) {\n      throw new Error(error.message);\n    }\n  };\n\n  const signOut = async () => {\n    const { error } = await supabase.auth.signOut();\n    if (error) {\n      throw new Error(error.message);\n    }\n  };\n\n  const value = {\n    user,\n    loading,\n    signInWithGoogle,\n    signOut,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAaA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,4IAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI;0CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;oBACpD,QAAQ,SAAS,QAAQ;oBACzB,WAAW;gBACb;;YAEA,0BAA0B;YAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,4IAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAAC,CAAC,QAAQ;oBAC3C,QAAQ,SAAS,QAAQ;oBACzB,WAAW;gBACb;;YAEA;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,4IAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;YACpD,UAAU;YACV,SAAS;gBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;YACvD;QACF;QAEA,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;IACF;IAEA,MAAM,UAAU;QACd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,4IAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GAlDgB;KAAA;AAoDT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}