-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table (extends Supabase auth.users)
CREATE TABLE public.users (
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL,
  name TEXT NOT NULL,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create agents table
CREATE TABLE public.agents (
  agent_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(user_id) ON DELETE CASCADE NOT NULL,
  agent_name TEXT NOT NULL,
  model_provider TEXT NOT NULL CHECK (model_provider IN ('OpenAI', 'OpenRouter')),
  model_name TEXT NOT NULL,
  api_key TEXT, -- Encrypted API key
  system_instructions TEXT NOT NULL,
  creation_date TIM<PERSON><PERSON><PERSON> WITH TIME ZONE DEFAULT NOW(),
  last_modified_date TIM<PERSON><PERSON>MP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> chat_threads table
CREATE TABLE public.chat_threads (
  chat_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  agent_id UUID REFERENCES public.agents(agent_id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES public.users(user_id) ON DELETE CASCADE NOT NULL,
  start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_updated_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  title TEXT
);

-- Create messages table
CREATE TABLE public.messages (
  message_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  chat_id UUID REFERENCES public.chat_threads(chat_id) ON DELETE CASCADE NOT NULL,
  sender TEXT NOT NULL CHECK (sender IN ('user', 'agent')),
  content TEXT NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create shared_agent_links table
CREATE TABLE public.shared_agent_links (
  link_id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  agent_id UUID REFERENCES public.agents(agent_id) ON DELETE CASCADE NOT NULL,
  author_user_id UUID REFERENCES public.users(user_id) ON DELETE CASCADE NOT NULL,
  share_mode TEXT NOT NULL CHECK (share_mode IN ('use', 'view_clone')),
  creation_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_agents_user_id ON public.agents(user_id);
CREATE INDEX idx_chat_threads_agent_id ON public.chat_threads(agent_id);
CREATE INDEX idx_chat_threads_user_id ON public.chat_threads(user_id);
CREATE INDEX idx_messages_chat_id ON public.messages(chat_id);
CREATE INDEX idx_shared_agent_links_agent_id ON public.shared_agent_links(agent_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_threads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.shared_agent_links ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Users can only see and modify their own user record
CREATE POLICY "Users can view own user data" ON public.users
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own user data" ON public.users
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own user data" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can only see and modify their own agents
CREATE POLICY "Users can view own agents" ON public.agents
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own agents" ON public.agents
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own agents" ON public.agents
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own agents" ON public.agents
  FOR DELETE USING (auth.uid() = user_id);

-- Users can only see and modify their own chat threads
CREATE POLICY "Users can view own chat threads" ON public.chat_threads
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own chat threads" ON public.chat_threads
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own chat threads" ON public.chat_threads
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own chat threads" ON public.chat_threads
  FOR DELETE USING (auth.uid() = user_id);

-- Users can only see messages from their own chat threads
CREATE POLICY "Users can view messages from own chats" ON public.messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.chat_threads 
      WHERE chat_threads.chat_id = messages.chat_id 
      AND chat_threads.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert messages to own chats" ON public.messages
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.chat_threads 
      WHERE chat_threads.chat_id = messages.chat_id 
      AND chat_threads.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete messages from own chats" ON public.messages
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.chat_threads 
      WHERE chat_threads.chat_id = messages.chat_id 
      AND chat_threads.user_id = auth.uid()
    )
  );

-- Shared agent links policies
CREATE POLICY "Users can view own shared links" ON public.shared_agent_links
  FOR SELECT USING (auth.uid() = author_user_id);

CREATE POLICY "Users can create shared links for own agents" ON public.shared_agent_links
  FOR INSERT WITH CHECK (
    auth.uid() = author_user_id AND
    EXISTS (
      SELECT 1 FROM public.agents 
      WHERE agents.agent_id = shared_agent_links.agent_id 
      AND agents.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete own shared links" ON public.shared_agent_links
  FOR DELETE USING (auth.uid() = author_user_id);

-- Function to automatically create user record on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (user_id, email, name, avatar_url)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
    NEW.raw_user_meta_data->>'avatar_url'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user record on signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update last_modified_date on agents
CREATE OR REPLACE FUNCTION public.update_agent_modified_date()
RETURNS TRIGGER AS $$
BEGIN
  NEW.last_modified_date = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update last_modified_date on agents
CREATE TRIGGER update_agent_modified_date
  BEFORE UPDATE ON public.agents
  FOR EACH ROW EXECUTE FUNCTION public.update_agent_modified_date();
