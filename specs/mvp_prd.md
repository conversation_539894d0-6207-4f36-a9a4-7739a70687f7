Product Requirements Document: Agent Director MVP
1. Introduction

Agent Director is an application designed to empower users to create, configure, manage, and share AI agents (personas/workflows). Users can define an agent's behavior by selecting AI models, crafting system instructions, and then interact with these agents through a chat interface. The MVP will include both a desktop application for local use and a web application for remote access, persistence, and sharing.

2. Goals for MVP

Core Functionality: Enable users to create custom AI agents by specifying a model and system instructions.
Interaction: Provide a functional chat interface for users to interact with their created agents, including conversation history management and streaming responses.
Persistence:
Desktop: Store agent configurations and chat history locally.
Web: Store agent configurations and chat history remotely, linked to a user account.
Sharing (Web): Allow users to share their agents via a link, with options to allow others to either just use (chat with) the agent or view its configuration and clone it.
Cross-Platform Foundation: Establish the basic architecture for both desktop and web applications to ensure alignment with long-term goals.
Validation: Test the core hypothesis that users find value in crafting and sharing personalized AI agents.
3. Target Audience (MVP)

Primary: AI enthusiasts who are interested in experimenting with different AI personas and sharing their creations.
Initial Test Group: The developer (yourself) to refine the core experience and identify immediate usability improvements.
4. Proposed Features (MVP Scope)

4.1. Core Agent & Chat Functionality (Common to Desktop & Web)

CA1: Agent Creation:
Users can create a new agent.
Assign a name to the agent.
Select an LLM model for the agent:
Option to integrate with OpenRouter (details TBD - e.g., user provides their OpenRouter API key globally or per agent).
Option to provide a direct API key for a supported model (e.g., OpenAI).
Provide system instructions (plain text input).
No configurable inference parameters for MVP (models will use defaults).
CA2: Agent Listing & Management:
View a list of created agents.
Edit an existing agent's configuration (name, model, system instructions).
Delete an agent.
CA3: Chat Interface:
Select an agent to chat with.
Input field for user messages.
Display conversation history with the selected agent (chronological order, differentiating user and agent messages).
Responses from the AI agent should stream in real-time (words appear as they are generated).
CA4: Conversation Management:
Ability to start a new conversation thread with an agent (clearing previous chat context for that session but not deleting history).
View a list of previous conversation threads for each agent.
Select and continue a previous conversation thread.
4.2. Desktop Application Specifics

DA1: Local Persistence:
Agent configurations will be stored locally on the user's machine (SQLite database).
Chat history for each agent will be stored locally (SQLite database).
No user login required for the desktop app in MVP.
DA2: Platform:
Built using Electron.
4.3. Web Application Specifics

WA1: User Authentication:
Users must sign up/log in to use the web application.
Primary login method: Google Sign-In.
(Optional, if easy to implement: consider 1-2 other common OAuth providers).
WA2: Remote Persistence:
Agent configurations created by a logged-in user will be stored remotely, associated with their account.
Chat history for each agent will be stored remotely, associated with the user's account and the specific agent.
WA3: Agent Sharing via Link:
Logged-in users can generate a unique shareable link for an agent they've created.
When sharing, the author can choose one of two modes:
Use Mode: Anyone with the link can chat with the agent. The agent runs using the author's configured model/API key context. The guest user does not see the agent's configuration. Chat history for these guest interactions is not saved for the guest. (Consider if the author sees these interactions).
View & Clone Mode: Anyone with the link can view the agent's configuration (model, system instructions). Logged-in users can then "clone" this configuration to their own account, creating a new, editable copy.
WA4: Accessing Shared Agents:
Users with a "Use Mode" link can interact with the agent without needing to log in.
Users with a "View & Clone Mode" link can view the configuration. If they wish to clone, they will need to be logged in.
5. Data Management

Agent Configuration:
agent_id (unique identifier)
user_id (for web app, null/not applicable for desktop local)
agent_name (text)
model_provider (e.g., "OpenRouter", "OpenAI")
model_name (text, e.g., "gpt-3.5-turbo")
api_key (encrypted, if user provides directly; consider how this is handled securely, especially for web)
system_instructions (text)
creation_date
last_modified_date
Chat History:
chat_id (unique identifier for a conversation thread)
agent_id (foreign key)
user_id (for web app)
start_time
last_updated_time
title (optional, could be auto-generated from first few messages)
Messages:
message_id (unique identifier)
chat_id (foreign key)
sender ("user" or "agent")
content (text)
timestamp
Shared Agent Links (Web App):
link_id (unique public identifier)
agent_id (foreign key)
author_user_id (foreign key)
share_mode ("use" or "view_clone")
creation_date
6. Technical Considerations (High-Level)

Desktop: Electron, SQLite.
Web Frontend: (User's choice - e.g., React, Vue, Angular, Svelte).
Web Backend: (User's choice - e.g., Node.js/Express, Python/Django/FastAPI, Firebase, Supabase). Needs to handle API requests, database interactions, user auth, and interaction with LLM APIs (potentially proxying to avoid exposing keys on the client for shared "use" mode).
LLM Interaction: Backend (for web) or directly from client (for desktop, if user provides key) will make API calls to selected model providers. Ensure secure handling of API keys, especially for the web app where agents can be shared for use by others (runs on author's key).
Streaming: Implement Server-Sent Events (SSE) or WebSockets for streaming responses in the chat interface for both web and desktop (Electron can support this).
7. Non-Goals for MVP (Out of Scope)

Configurable inference parameters (e.g., temperature, max tokens, top-p).
Agent "workflows" or chaining of agents/prompts.
Advanced templating or variable support in system instructions.
Image generation or multi-modal models.
Organization/folder management for agents.
Usage analytics for shared agents (for the author).
Rate limiting or advanced security measures beyond basic auth for shared agents (though this should be considered for V2).
Import/Export of agent configurations as files (sharing is link-based for MVP).
Real-time collaboration on agent configuration.
8. Success Metrics for MVP (Qualitative & Early Quantitative)

Successfully create and chat with agents on both desktop and web platforms.
Successfully share an agent via a link from the web app, and have another user interact with it (use mode) or clone it (view & clone mode).
Internal feedback: Is the core loop of creating, chatting, and sharing intuitive and functional for the initial user (developer)?
(Post-MVP marketing) Number of agents created and shared, if opened to a wider audience.
This PRD should provide a solid foundation. Remember that a PRD is a living document; you may need to adjust it as you delve into development and encounter unforeseen challenges or opportunities.

What are your thoughts on this initial PRD draft? Is there anything crucial missing or anything that needs further refinement for the MVP?
