export interface Agent {
    agent_id: string;
    user_id?: string;
    agent_name: string;
    model_provider: 'OpenRouter' | 'OpenAI';
    model_name: string;
    api_key?: string;
    system_instructions: string;
    creation_date: Date;
    last_modified_date: Date;
}
export interface CreateAgentRequest {
    agent_name: string;
    model_provider: 'OpenRouter' | 'OpenAI';
    model_name: string;
    api_key?: string;
    system_instructions: string;
}
export interface UpdateAgentRequest {
    agent_id: string;
    agent_name?: string;
    model_provider?: 'OpenRouter' | 'OpenAI';
    model_name?: string;
    api_key?: string;
    system_instructions?: string;
}
export interface ChatThread {
    chat_id: string;
    agent_id: string;
    user_id?: string;
    start_time: Date;
    last_updated_time: Date;
    title?: string;
}
export interface Message {
    message_id: string;
    chat_id: string;
    sender: 'user' | 'agent';
    content: string;
    timestamp: Date;
}
export interface CreateChatRequest {
    agent_id: string;
    title?: string;
}
export interface SendMessageRequest {
    chat_id: string;
    content: string;
}
export interface SharedAgentLink {
    link_id: string;
    agent_id: string;
    author_user_id: string;
    share_mode: 'use' | 'view_clone';
    creation_date: Date;
}
export interface CreateShareLinkRequest {
    agent_id: string;
    share_mode: 'use' | 'view_clone';
}
export interface User {
    user_id: string;
    email: string;
    name: string;
    avatar_url?: string;
    created_at: Date;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
}
export interface LLMProvider {
    name: string;
    models: LLMModel[];
    requiresApiKey: boolean;
}
export interface LLMModel {
    id: string;
    name: string;
    description?: string;
    maxTokens?: number;
}
export interface StreamingResponse {
    type: 'token' | 'done' | 'error';
    content?: string;
    error?: string;
}
export interface DatabaseAdapter {
    createAgent(agent: Omit<Agent, 'agent_id' | 'creation_date' | 'last_modified_date'>): Promise<Agent>;
    getAgent(agentId: string, userId?: string): Promise<Agent | null>;
    listAgents(userId?: string): Promise<Agent[]>;
    updateAgent(agentId: string, updates: Partial<Agent>, userId?: string): Promise<Agent>;
    deleteAgent(agentId: string, userId?: string): Promise<boolean>;
    createChat(chat: Omit<ChatThread, 'chat_id' | 'start_time' | 'last_updated_time'>): Promise<ChatThread>;
    getChat(chatId: string, userId?: string): Promise<ChatThread | null>;
    listChats(agentId: string, userId?: string): Promise<ChatThread[]>;
    deleteChat(chatId: string, userId?: string): Promise<boolean>;
    addMessage(message: Omit<Message, 'message_id' | 'timestamp'>): Promise<Message>;
    getMessages(chatId: string, userId?: string): Promise<Message[]>;
    deleteMessages(chatId: string, userId?: string): Promise<boolean>;
}
//# sourceMappingURL=index.d.ts.map