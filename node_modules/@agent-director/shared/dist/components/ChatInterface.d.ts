import React from 'react';
import { Message, Agent, ChatThread } from '@agent-director/types';
interface ChatInterfaceProps {
    agent: Agent;
    currentChat?: ChatThread;
    messages: Message[];
    onSendMessage: (content: string) => Promise<void>;
    onNewChat: () => void;
    loading?: boolean;
    streamingMessage?: string;
}
export declare const ChatInterface: React.FC<ChatInterfaceProps>;
export {};
//# sourceMappingURL=ChatInterface.d.ts.map