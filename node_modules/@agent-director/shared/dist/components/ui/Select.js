"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Select = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const Select = ({ label, error, helperText, options, placeholder, className = '', id, ...props }) => {
    const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;
    const selectClasses = `
    block w-full px-3 py-2 border rounded-md shadow-sm 
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
    ${error ? 'border-red-300' : 'border-gray-300'}
    ${className}
  `.trim();
    return ((0, jsx_runtime_1.jsxs)("div", { className: "space-y-1", children: [label && ((0, jsx_runtime_1.jsx)("label", { htmlFor: selectId, className: "block text-sm font-medium text-gray-700", children: label })), (0, jsx_runtime_1.jsxs)("select", { id: selectId, className: selectClasses, ...props, children: [placeholder && ((0, jsx_runtime_1.jsx)("option", { value: "", disabled: true, children: placeholder })), options.map((option) => ((0, jsx_runtime_1.jsx)("option", { value: option.value, children: option.label }, option.value)))] }), error && ((0, jsx_runtime_1.jsx)("p", { className: "text-sm text-red-600", children: error })), helperText && !error && ((0, jsx_runtime_1.jsx)("p", { className: "text-sm text-gray-500", children: helperText }))] }));
};
exports.Select = Select;
//# sourceMappingURL=Select.js.map