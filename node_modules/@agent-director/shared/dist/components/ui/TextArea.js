"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextArea = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const TextArea = ({ label, error, helperText, className = '', id, ...props }) => {
    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;
    const textareaClasses = `
    block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
    resize-vertical min-h-[100px]
    ${error ? 'border-red-300' : 'border-gray-300'}
    ${className}
  `.trim();
    return ((0, jsx_runtime_1.jsxs)("div", { className: "space-y-1", children: [label && ((0, jsx_runtime_1.jsx)("label", { htmlFor: textareaId, className: "block text-sm font-medium text-gray-700", children: label })), (0, jsx_runtime_1.jsx)("textarea", { id: textareaId, className: textareaClasses, ...props }), error && ((0, jsx_runtime_1.jsx)("p", { className: "text-sm text-red-600", children: error })), helperText && !error && ((0, jsx_runtime_1.jsx)("p", { className: "text-sm text-gray-500", children: helperText }))] }));
};
exports.TextArea = TextArea;
//# sourceMappingURL=TextArea.js.map