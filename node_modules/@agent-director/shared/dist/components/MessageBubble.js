"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageBubble = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const lucide_react_1 = require("lucide-react");
const MessageBubble = ({ message, isStreaming = false }) => {
    const isUser = message.sender === 'user';
    return ((0, jsx_runtime_1.jsx)("div", { className: `flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`, children: (0, jsx_runtime_1.jsxs)("div", { className: `flex max-w-[80%] ${isUser ? 'flex-row-reverse' : 'flex-row'}`, children: [(0, jsx_runtime_1.jsx)("div", { className: `flex-shrink-0 ${isUser ? 'ml-3' : 'mr-3'}`, children: (0, jsx_runtime_1.jsx)("div", { className: `w-8 h-8 rounded-full flex items-center justify-center ${isUser ? 'bg-blue-500' : 'bg-gray-500'}`, children: isUser ? ((0, jsx_runtime_1.jsx)(lucide_react_1.User, { size: 16, className: "text-white" })) : ((0, jsx_runtime_1.jsx)(lucide_react_1.Bot, { size: 16, className: "text-white" })) }) }), (0, jsx_runtime_1.jsxs)("div", { className: `rounded-lg px-4 py-2 ${isUser
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-900'}`, children: [(0, jsx_runtime_1.jsxs)("div", { className: "whitespace-pre-wrap break-words", children: [message.content, isStreaming && ((0, jsx_runtime_1.jsx)("span", { className: "inline-block w-2 h-5 bg-current ml-1 animate-pulse" }))] }), (0, jsx_runtime_1.jsx)("div", { className: `text-xs mt-1 ${isUser ? 'text-blue-100' : 'text-gray-500'}`, children: new Date(message.timestamp).toLocaleTimeString([], {
                                hour: '2-digit',
                                minute: '2-digit'
                            }) })] })] }) }));
};
exports.MessageBubble = MessageBubble;
//# sourceMappingURL=MessageBubble.js.map