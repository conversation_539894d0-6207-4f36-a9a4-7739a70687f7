"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentForm = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = require("react");
const Input_1 = require("./ui/Input");
const TextArea_1 = require("./ui/TextArea");
const Select_1 = require("./ui/Select");
const Button_1 = require("./ui/Button");
const AgentForm = ({ agent, providers, onSubmit, onCancel, loading = false }) => {
    const [formData, setFormData] = (0, react_1.useState)({
        agent_name: agent?.agent_name || '',
        model_provider: agent?.model_provider || 'OpenAI',
        model_name: agent?.model_name || '',
        api_key: agent?.api_key || '',
        system_instructions: agent?.system_instructions || ''
    });
    const [errors, setErrors] = (0, react_1.useState)({});
    const selectedProvider = providers.find(p => p.name === formData.model_provider);
    const handleSubmit = async (e) => {
        e.preventDefault();
        // Validation
        const newErrors = {};
        if (!formData.agent_name.trim()) {
            newErrors.agent_name = 'Agent name is required';
        }
        if (!formData.model_name) {
            newErrors.model_name = 'Model selection is required';
        }
        if (!formData.system_instructions.trim()) {
            newErrors.system_instructions = 'System instructions are required';
        }
        if (selectedProvider?.requiresApiKey && !formData.api_key.trim()) {
            newErrors.api_key = 'API key is required for this provider';
        }
        if (Object.keys(newErrors).length > 0) {
            setErrors(newErrors);
            return;
        }
        setErrors({});
        const submitData = agent
            ? { agent_id: agent.agent_id, ...formData }
            : formData;
        await onSubmit(submitData);
    };
    const handleChange = (field, value) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }
    };
    return ((0, jsx_runtime_1.jsxs)("form", { onSubmit: handleSubmit, className: "space-y-6", children: [(0, jsx_runtime_1.jsx)(Input_1.Input, { label: "Agent Name", value: formData.agent_name, onChange: (e) => handleChange('agent_name', e.target.value), error: errors.agent_name, placeholder: "Enter a name for your agent", required: true }), (0, jsx_runtime_1.jsx)(Select_1.Select, { label: "Model Provider", value: formData.model_provider, onChange: (e) => handleChange('model_provider', e.target.value), options: providers.map(p => ({ value: p.name, label: p.name })), error: errors.model_provider, required: true }), selectedProvider && ((0, jsx_runtime_1.jsx)(Select_1.Select, { label: "Model", value: formData.model_name, onChange: (e) => handleChange('model_name', e.target.value), options: selectedProvider.models.map(m => ({ value: m.id, label: m.name })), placeholder: "Select a model", error: errors.model_name, required: true })), selectedProvider?.requiresApiKey && ((0, jsx_runtime_1.jsx)(Input_1.Input, { label: "API Key", type: "password", value: formData.api_key, onChange: (e) => handleChange('api_key', e.target.value), error: errors.api_key, placeholder: "Enter your API key", helperText: "Your API key will be stored securely", required: true })), (0, jsx_runtime_1.jsx)(TextArea_1.TextArea, { label: "System Instructions", value: formData.system_instructions, onChange: (e) => handleChange('system_instructions', e.target.value), error: errors.system_instructions, placeholder: "Define how your agent should behave...", rows: 6, required: true }), (0, jsx_runtime_1.jsxs)("div", { className: "flex justify-end space-x-3", children: [(0, jsx_runtime_1.jsx)(Button_1.Button, { type: "button", variant: "secondary", onClick: onCancel, children: "Cancel" }), (0, jsx_runtime_1.jsx)(Button_1.Button, { type: "submit", loading: loading, children: agent ? 'Update Agent' : 'Create Agent' })] })] }));
};
exports.AgentForm = AgentForm;
//# sourceMappingURL=AgentForm.js.map