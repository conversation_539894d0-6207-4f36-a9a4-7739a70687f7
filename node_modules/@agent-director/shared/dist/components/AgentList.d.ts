import React from 'react';
import { Agent } from '@agent-director/types';
interface AgentListProps {
    agents: Agent[];
    onEdit: (agent: Agent) => void;
    onDelete: (agentId: string) => void;
    onChat: (agent: Agent) => void;
    onShare?: (agent: Agent) => void;
    loading?: boolean;
}
export declare const AgentList: React.FC<AgentListProps>;
export {};
//# sourceMappingURL=AgentList.d.ts.map