import React from 'react';
import { Agent, CreateAgentRequest, UpdateAgentRequest, LLMProvider } from '@agent-director/types';
interface AgentFormProps {
    agent?: Agent;
    providers: LLMProvider[];
    onSubmit: (data: CreateAgentRequest | UpdateAgentRequest) => Promise<void>;
    onCancel: () => void;
    loading?: boolean;
}
export declare const AgentForm: React.FC<AgentFormProps>;
export {};
//# sourceMappingURL=AgentForm.d.ts.map