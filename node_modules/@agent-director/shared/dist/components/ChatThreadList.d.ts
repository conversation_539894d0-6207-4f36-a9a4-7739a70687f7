import React from 'react';
import { ChatThread } from '@agent-director/types';
interface ChatThreadListProps {
    threads: ChatThread[];
    currentThreadId?: string;
    onSelectThread: (thread: ChatThread) => void;
    onDeleteThread: (threadId: string) => void;
    loading?: boolean;
}
export declare const ChatThreadList: React.FC<ChatThreadListProps>;
export {};
//# sourceMappingURL=ChatThreadList.d.ts.map