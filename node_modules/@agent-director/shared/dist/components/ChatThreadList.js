"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatThreadList = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const Button_1 = require("./ui/Button");
const lucide_react_1 = require("lucide-react");
const ChatThreadList = ({ threads, currentThreadId, onSelectThread, onDeleteThread, loading = false }) => {
    if (loading) {
        return ((0, jsx_runtime_1.jsx)("div", { className: "space-y-2", children: [...Array(3)].map((_, i) => ((0, jsx_runtime_1.jsx)("div", { className: "animate-pulse", children: (0, jsx_runtime_1.jsx)("div", { className: "bg-gray-200 rounded h-16" }) }, i))) }));
    }
    if (threads.length === 0) {
        return ((0, jsx_runtime_1.jsxs)("div", { className: "text-center py-8 text-gray-500", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.MessageCircle, { size: 48, className: "mx-auto mb-3 text-gray-300" }), (0, jsx_runtime_1.jsx)("p", { children: "No conversations yet" }), (0, jsx_runtime_1.jsx)("p", { className: "text-sm", children: "Start chatting to create conversation threads" })] }));
    }
    return ((0, jsx_runtime_1.jsx)("div", { className: "space-y-2", children: threads.map((thread) => ((0, jsx_runtime_1.jsx)("div", { className: `group border rounded-lg p-3 cursor-pointer transition-colors ${currentThreadId === thread.chat_id
                ? 'bg-blue-50 border-blue-200'
                : 'bg-white hover:bg-gray-50'}`, onClick: () => onSelectThread(thread), children: (0, jsx_runtime_1.jsxs)("div", { className: "flex justify-between items-start", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex-1 min-w-0", children: [(0, jsx_runtime_1.jsx)("h4", { className: "font-medium text-gray-900 truncate", children: thread.title || 'Untitled Conversation' }), (0, jsx_runtime_1.jsxs)("p", { className: "text-sm text-gray-500 mt-1", children: [new Date(thread.last_updated_time).toLocaleDateString(), " at", ' ', new Date(thread.last_updated_time).toLocaleTimeString([], {
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    })] })] }), (0, jsx_runtime_1.jsx)(Button_1.Button, { size: "sm", variant: "ghost", onClick: (e) => {
                            e.stopPropagation();
                            onDeleteThread(thread.chat_id);
                        }, className: "opacity-0 group-hover:opacity-100 transition-opacity", title: "Delete conversation", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Trash2, { size: 14 }) })] }) }, thread.chat_id))) }));
};
exports.ChatThreadList = ChatThreadList;
//# sourceMappingURL=ChatThreadList.js.map