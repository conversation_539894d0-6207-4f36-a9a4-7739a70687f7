"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatInterface = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = require("react");
const MessageBubble_1 = require("./MessageBubble");
const Button_1 = require("./ui/Button");
const lucide_react_1 = require("lucide-react");
const ChatInterface = ({ agent, currentChat, messages, onSendMessage, onNewChat, loading = false, streamingMessage }) => {
    const [input, setInput] = (0, react_1.useState)('');
    const [sending, setSending] = (0, react_1.useState)(false);
    const messagesEndRef = (0, react_1.useRef)(null);
    const textareaRef = (0, react_1.useRef)(null);
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };
    (0, react_1.useEffect)(() => {
        scrollToBottom();
    }, [messages, streamingMessage]);
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!input.trim() || sending)
            return;
        const messageContent = input.trim();
        setInput('');
        setSending(true);
        try {
            await onSendMessage(messageContent);
        }
        finally {
            setSending(false);
        }
    };
    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit(e);
        }
    };
    // Auto-resize textarea
    (0, react_1.useEffect)(() => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
        }
    }, [input]);
    return ((0, jsx_runtime_1.jsxs)("div", { className: "flex flex-col h-full", children: [(0, jsx_runtime_1.jsxs)("div", { className: "border-b bg-white px-6 py-4 flex justify-between items-center", children: [(0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("h2", { className: "text-lg font-semibold text-gray-900", children: agent.agent_name }), (0, jsx_runtime_1.jsxs)("p", { className: "text-sm text-gray-500", children: [agent.model_provider, " \u2022 ", agent.model_name] })] }), (0, jsx_runtime_1.jsxs)(Button_1.Button, { variant: "secondary", size: "sm", onClick: onNewChat, disabled: loading, children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Plus, { size: 16, className: "mr-1" }), "New Chat"] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "flex-1 overflow-y-auto p-6 space-y-4", children: [messages.length === 0 && !streamingMessage && ((0, jsx_runtime_1.jsxs)("div", { className: "text-center text-gray-500 mt-8", children: [(0, jsx_runtime_1.jsxs)("p", { className: "text-lg mb-2", children: ["Start a conversation with ", agent.agent_name] }), (0, jsx_runtime_1.jsx)("p", { className: "text-sm", children: "Type a message below to begin" })] })), messages.map((message) => ((0, jsx_runtime_1.jsx)(MessageBubble_1.MessageBubble, { message: message }, message.message_id))), streamingMessage && ((0, jsx_runtime_1.jsx)(MessageBubble_1.MessageBubble, { message: {
                            message_id: 'streaming',
                            chat_id: currentChat?.chat_id || '',
                            sender: 'agent',
                            content: streamingMessage,
                            timestamp: new Date()
                        }, isStreaming: true })), (0, jsx_runtime_1.jsx)("div", { ref: messagesEndRef })] }), (0, jsx_runtime_1.jsx)("div", { className: "border-t bg-white p-6", children: (0, jsx_runtime_1.jsxs)("form", { onSubmit: handleSubmit, className: "flex space-x-3", children: [(0, jsx_runtime_1.jsx)("div", { className: "flex-1", children: (0, jsx_runtime_1.jsx)("textarea", { ref: textareaRef, value: input, onChange: (e) => setInput(e.target.value), onKeyDown: handleKeyDown, placeholder: "Type your message...", className: "w-full px-3 py-2 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 max-h-32", rows: 1, disabled: sending || loading }) }), (0, jsx_runtime_1.jsx)(Button_1.Button, { type: "submit", disabled: !input.trim() || sending || loading, loading: sending, children: (0, jsx_runtime_1.jsx)(lucide_react_1.Send, { size: 16 }) })] }) })] }));
};
exports.ChatInterface = ChatInterface;
//# sourceMappingURL=ChatInterface.js.map