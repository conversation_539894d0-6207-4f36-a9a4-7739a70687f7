"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentList = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const Button_1 = require("./ui/Button");
const lucide_react_1 = require("lucide-react");
const AgentList = ({ agents, onEdit, onDelete, onChat, onShare, loading = false }) => {
    if (loading) {
        return ((0, jsx_runtime_1.jsx)("div", { className: "space-y-4", children: [...Array(3)].map((_, i) => ((0, jsx_runtime_1.jsx)("div", { className: "animate-pulse", children: (0, jsx_runtime_1.jsx)("div", { className: "bg-gray-200 rounded-lg h-32" }) }, i))) }));
    }
    if (agents.length === 0) {
        return ((0, jsx_runtime_1.jsxs)("div", { className: "text-center py-12", children: [(0, jsx_runtime_1.jsx)("div", { className: "text-gray-500 text-lg mb-2", children: "No agents created yet" }), (0, jsx_runtime_1.jsx)("div", { className: "text-gray-400", children: "Create your first AI agent to get started" })] }));
    }
    return ((0, jsx_runtime_1.jsx)("div", { className: "space-y-4", children: agents.map((agent) => ((0, jsx_runtime_1.jsxs)("div", { className: "bg-white border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow", children: [(0, jsx_runtime_1.jsx)("div", { className: "flex justify-between items-start mb-4", children: (0, jsx_runtime_1.jsxs)("div", { className: "flex-1", children: [(0, jsx_runtime_1.jsx)("h3", { className: "text-lg font-semibold text-gray-900 mb-1", children: agent.agent_name }), (0, jsx_runtime_1.jsxs)("div", { className: "text-sm text-gray-500 mb-2", children: [agent.model_provider, " \u2022 ", agent.model_name] }), (0, jsx_runtime_1.jsx)("p", { className: "text-gray-600 text-sm line-clamp-2", children: agent.system_instructions })] }) }), (0, jsx_runtime_1.jsxs)("div", { className: "flex justify-between items-center", children: [(0, jsx_runtime_1.jsxs)("div", { className: "text-xs text-gray-400", children: ["Created ", new Date(agent.creation_date).toLocaleDateString()] }), (0, jsx_runtime_1.jsxs)("div", { className: "flex space-x-2", children: [(0, jsx_runtime_1.jsx)(Button_1.Button, { size: "sm", variant: "ghost", onClick: () => onChat(agent), title: "Chat with agent", children: (0, jsx_runtime_1.jsx)(lucide_react_1.MessageCircle, { size: 16 }) }), onShare && ((0, jsx_runtime_1.jsx)(Button_1.Button, { size: "sm", variant: "ghost", onClick: () => onShare(agent), title: "Share agent", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Share, { size: 16 }) })), (0, jsx_runtime_1.jsx)(Button_1.Button, { size: "sm", variant: "ghost", onClick: () => onEdit(agent), title: "Edit agent", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Edit, { size: 16 }) }), (0, jsx_runtime_1.jsx)(Button_1.Button, { size: "sm", variant: "ghost", onClick: () => onDelete(agent.agent_id), title: "Delete agent", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Trash2, { size: 16 }) })] })] })] }, agent.agent_id))) }));
};
exports.AgentList = AgentList;
//# sourceMappingURL=AgentList.js.map