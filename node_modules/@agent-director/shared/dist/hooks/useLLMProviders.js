"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useLLMProviders = useLLMProviders;
const react_1 = require("react");
const llm_1 = require("../utils/llm");
function useLLMProviders() {
    const [providers, setProviders] = (0, react_1.useState)(llm_1.DEFAULT_PROVIDERS);
    const [loading, setLoading] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)(null);
    // In the future, this could fetch providers from an API
    const loadProviders = async () => {
        setLoading(true);
        setError(null);
        try {
            // For now, just use the default providers
            // In the future, this could fetch from an API to get updated model lists
            setProviders(llm_1.DEFAULT_PROVIDERS);
        }
        catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load providers');
        }
        finally {
            setLoading(false);
        }
    };
    const getProvider = (name) => {
        return providers.find(p => p.name === name);
    };
    const getModel = (providerName, modelId) => {
        const provider = getProvider(providerName);
        return provider?.models.find(m => m.id === modelId);
    };
    (0, react_1.useEffect)(() => {
        loadProviders();
    }, []);
    return {
        providers,
        loading,
        error,
        getProvider,
        getModel,
        refreshProviders: loadProviders
    };
}
//# sourceMappingURL=useLLMProviders.js.map