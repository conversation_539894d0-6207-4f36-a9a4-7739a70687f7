{"version": 3, "file": "useChat.js", "sourceRoot": "", "sources": ["../../src/hooks/useChat.ts"], "names": [], "mappings": ";;AAIA,0BAoLC;AAxLD,iCAA4C;AAE5C,sCAAyC;AAEzC,SAAgB,OAAO,CACrB,QAAyB,EACzB,KAAmB,EACnB,MAAe;IAEf,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,IAAA,gBAAQ,EAAe,EAAE,CAAC,CAAC;IACzD,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,IAAA,gBAAQ,EAAoB,IAAI,CAAC,CAAC;IAC5E,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,IAAA,gBAAQ,EAAY,EAAE,CAAC,CAAC;IACxD,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAC9C,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,IAAA,gBAAQ,EAAgB,IAAI,CAAC,CAAC;IACxD,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,IAAA,gBAAQ,EAAS,EAAE,CAAC,CAAC;IACrE,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAE9C,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;QAC7B,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,UAAU,CAAC,IAAI,CAAC,CAAC;QACjB,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEf,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACpE,UAAU,CAAC,UAAU,CAAC,CAAC;QACzB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,QAAQ,CAAC,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC;QAC/E,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,KAAK,EAAE,QAAgB,EAAE,EAAE;QAC9C,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEf,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACjE,WAAW,CAAC,WAAW,CAAC,CAAC;QAC3B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,QAAQ,CAAC,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,KAAK,IAAyB,EAAE;QACtD,IAAI,CAAC,KAAK;YAAE,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAEjD,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEf,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,UAAU,CAAC;gBACvC,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,kBAAkB;aAC1B,CAAC,CAAC;YAEH,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;YACtC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YACzB,WAAW,CAAC,EAAE,CAAC,CAAC;YAEhB,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B,CAAC;YACzF,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAChC,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,KAAK,EAAE,MAAkB,EAAE,EAAE;QAChD,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACzB,MAAM,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,KAAK,EAAE,QAAgB,EAAE,EAAE;QAC9C,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEf,IAAI,CAAC;YACH,MAAM,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC5C,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC;YAE7D,IAAI,aAAa,EAAE,OAAO,KAAK,QAAQ,EAAE,CAAC;gBACxC,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBACvB,WAAW,CAAC,EAAE,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B,CAAC;YACzF,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAChC,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,KAAK,EAAE,OAAe,EAAiB,EAAE;QAC3D,IAAI,CAAC,KAAK,IAAI,CAAC,aAAa,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,UAAU,CAAC,IAAI,CAAC,CAAC;QACjB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAExB,IAAI,CAAC;YACH,mBAAmB;YACnB,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,UAAU,CAAC;gBAC5C,OAAO,EAAE,aAAa,CAAC,OAAO;gBAC9B,MAAM,EAAE,MAAM;gBACd,OAAO;aACR,CAAC,CAAC;YAEH,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;YAE5C,2BAA2B;YAC3B,MAAM,oBAAoB,GAAG;gBAC3B,EAAE,IAAI,EAAE,QAAiB,EAAE,OAAO,EAAE,KAAK,CAAC,mBAAmB,EAAE;gBAC/D,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBACtB,IAAI,EAAE,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,MAAe,CAAC,CAAC,CAAC,WAAoB;oBACpE,OAAO,EAAE,GAAG,CAAC,OAAO;iBACrB,CAAC,CAAC;gBACH,EAAE,IAAI,EAAE,MAAe,EAAE,OAAO,EAAE;aACnC,CAAC;YAEF,kCAAkC;YAClC,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC;YAC7B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;YAC1D,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,eAAS,CAAC,MAAM,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC;YAE9D,kBAAkB;YAClB,IAAI,YAAY,GAAG,EAAE,CAAC;YAEtB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,SAAS,CAAC,oBAAoB,CAAC;gBACvD,KAAK,EAAE,KAAK,CAAC,UAAU;gBACvB,QAAQ,EAAE,oBAAoB;gBAC9B,MAAM,EAAE,IAAI;aACb,CAAC,EAAE,CAAC;gBACH,YAAY,IAAI,KAAK,CAAC;gBACtB,mBAAmB,CAAC,YAAY,CAAC,CAAC;YACpC,CAAC;YAED,sBAAsB;YACtB,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,UAAU,CAAC;gBAC7C,OAAO,EAAE,aAAa,CAAC,OAAO;gBAC9B,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,YAAY;aACtB,CAAC,CAAC;YAEH,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;YAC7C,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAE1B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC;YACnF,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,mBAAmB,CAAC,EAAE,CAAC,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAChC,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC;IACH,CAAC,CAAC;IAEF,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,IAAI,KAAK,EAAE,CAAC;YACV,WAAW,EAAE,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,UAAU,CAAC,EAAE,CAAC,CAAC;YACf,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACvB,WAAW,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;IAE9B,OAAO;QACL,OAAO;QACP,aAAa;QACb,QAAQ;QACR,OAAO;QACP,KAAK;QACL,gBAAgB;QAChB,OAAO;QACP,eAAe;QACf,YAAY;QACZ,YAAY;QACZ,WAAW;QACX,cAAc,EAAE,WAAW;KAC5B,CAAC;AACJ,CAAC"}