import { Agent, CreateAgentRequest, UpdateAgentRequest, DatabaseAdapter } from '@agent-director/types';
export declare function useAgent(database: DatabaseAdapter, userId?: string): {
    agents: Agent[];
    loading: boolean;
    error: string | null;
    createAgent: (data: CreateAgentRequest) => Promise<Agent>;
    updateAgent: (agentId: string, data: Partial<UpdateAgentRequest>) => Promise<Agent>;
    deleteAgent: (agentId: string) => Promise<void>;
    getAgent: (agentId: string) => Promise<Agent | null>;
    refreshAgents: () => Promise<void>;
};
//# sourceMappingURL=useAgent.d.ts.map