import { ChatThread, Message, Agent, DatabaseAdapter } from '@agent-director/types';
export declare function useChat(database: DatabaseAdapter, agent: Agent | null, userId?: string): {
    threads: ChatThread[];
    currentThread: ChatThread | null;
    messages: Message[];
    loading: boolean;
    error: string | null;
    streamingMessage: string;
    sending: boolean;
    createNewThread: () => Promise<ChatThread>;
    selectThread: (thread: ChatThread) => Promise<void>;
    deleteThread: (threadId: string) => Promise<void>;
    sendMessage: (content: string) => Promise<void>;
    refreshThreads: () => Promise<void>;
};
//# sourceMappingURL=useChat.d.ts.map