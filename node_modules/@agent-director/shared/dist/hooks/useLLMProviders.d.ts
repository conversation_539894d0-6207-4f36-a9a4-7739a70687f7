import { LLMProvider } from '@agent-director/types';
export declare function useLLMProviders(): {
    providers: LLMProvider[];
    loading: boolean;
    error: string | null;
    getProvider: (name: string) => LLMProvider | undefined;
    getModel: (providerName: string, modelId: string) => import("@agent-director/types").LLMModel | undefined;
    refreshProviders: () => Promise<void>;
};
//# sourceMappingURL=useLLMProviders.d.ts.map