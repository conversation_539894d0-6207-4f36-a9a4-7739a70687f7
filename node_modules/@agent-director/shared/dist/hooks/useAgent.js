"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useAgent = useAgent;
const react_1 = require("react");
function useAgent(database, userId) {
    const [agents, setAgents] = (0, react_1.useState)([]);
    const [loading, setLoading] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)(null);
    const loadAgents = async () => {
        setLoading(true);
        setError(null);
        try {
            const agentList = await database.listAgents(userId);
            setAgents(agentList);
        }
        catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load agents');
        }
        finally {
            setLoading(false);
        }
    };
    const createAgent = async (data) => {
        setError(null);
        try {
            const agent = await database.createAgent({
                ...data,
                user_id: userId
            });
            setAgents(prev => [...prev, agent]);
            return agent;
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to create agent';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    };
    const updateAgent = async (agentId, data) => {
        setError(null);
        try {
            const updatedAgent = await database.updateAgent(agentId, data, userId);
            setAgents(prev => prev.map(agent => agent.agent_id === agentId ? updatedAgent : agent));
            return updatedAgent;
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to update agent';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    };
    const deleteAgent = async (agentId) => {
        setError(null);
        try {
            await database.deleteAgent(agentId, userId);
            setAgents(prev => prev.filter(agent => agent.agent_id !== agentId));
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to delete agent';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    };
    const getAgent = async (agentId) => {
        setError(null);
        try {
            return await database.getAgent(agentId, userId);
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to get agent';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    };
    (0, react_1.useEffect)(() => {
        loadAgents();
    }, [userId]);
    return {
        agents,
        loading,
        error,
        createAgent,
        updateAgent,
        deleteAgent,
        getAgent,
        refreshAgents: loadAgents
    };
}
//# sourceMappingURL=useAgent.js.map