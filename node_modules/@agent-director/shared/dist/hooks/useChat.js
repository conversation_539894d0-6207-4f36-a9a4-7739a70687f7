"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useChat = useChat;
const react_1 = require("react");
const llm_1 = require("../utils/llm");
function useChat(database, agent, userId) {
    const [threads, setThreads] = (0, react_1.useState)([]);
    const [currentThread, setCurrentThread] = (0, react_1.useState)(null);
    const [messages, setMessages] = (0, react_1.useState)([]);
    const [loading, setLoading] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)(null);
    const [streamingMessage, setStreamingMessage] = (0, react_1.useState)('');
    const [sending, setSending] = (0, react_1.useState)(false);
    const loadThreads = async () => {
        if (!agent)
            return;
        setLoading(true);
        setError(null);
        try {
            const threadList = await database.listChats(agent.agent_id, userId);
            setThreads(threadList);
        }
        catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load chat threads');
        }
        finally {
            setLoading(false);
        }
    };
    const loadMessages = async (threadId) => {
        setError(null);
        try {
            const messageList = await database.getMessages(threadId, userId);
            setMessages(messageList);
        }
        catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load messages');
        }
    };
    const createNewThread = async () => {
        if (!agent)
            throw new Error('No agent selected');
        setError(null);
        try {
            const thread = await database.createChat({
                agent_id: agent.agent_id,
                user_id: userId,
                title: 'New Conversation'
            });
            setThreads(prev => [thread, ...prev]);
            setCurrentThread(thread);
            setMessages([]);
            return thread;
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to create chat thread';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    };
    const selectThread = async (thread) => {
        setCurrentThread(thread);
        await loadMessages(thread.chat_id);
    };
    const deleteThread = async (threadId) => {
        setError(null);
        try {
            await database.deleteChat(threadId, userId);
            setThreads(prev => prev.filter(t => t.chat_id !== threadId));
            if (currentThread?.chat_id === threadId) {
                setCurrentThread(null);
                setMessages([]);
            }
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to delete chat thread';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    };
    const sendMessage = async (content) => {
        if (!agent || !currentThread) {
            throw new Error('No agent or chat thread selected');
        }
        setSending(true);
        setError(null);
        setStreamingMessage('');
        try {
            // Add user message
            const userMessage = await database.addMessage({
                chat_id: currentThread.chat_id,
                sender: 'user',
                content
            });
            setMessages(prev => [...prev, userMessage]);
            // Prepare messages for LLM
            const conversationMessages = [
                { role: 'system', content: agent.system_instructions },
                ...messages.map(msg => ({
                    role: msg.sender === 'user' ? 'user' : 'assistant',
                    content: msg.content
                })),
                { role: 'user', content }
            ];
            // Get API key (decrypt if needed)
            const apiKey = agent.api_key;
            if (!apiKey) {
                throw new Error('No API key configured for this agent');
            }
            const llmClient = new llm_1.LLMClient(apiKey, agent.model_provider);
            // Stream response
            let fullResponse = '';
            for await (const chunk of llmClient.streamChatCompletion({
                model: agent.model_name,
                messages: conversationMessages,
                stream: true
            })) {
                fullResponse += chunk;
                setStreamingMessage(fullResponse);
            }
            // Save agent response
            const agentMessage = await database.addMessage({
                chat_id: currentThread.chat_id,
                sender: 'agent',
                content: fullResponse
            });
            setMessages(prev => [...prev, agentMessage]);
            setStreamingMessage('');
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
            setError(errorMessage);
            setStreamingMessage('');
            throw new Error(errorMessage);
        }
        finally {
            setSending(false);
        }
    };
    (0, react_1.useEffect)(() => {
        if (agent) {
            loadThreads();
        }
        else {
            setThreads([]);
            setCurrentThread(null);
            setMessages([]);
        }
    }, [agent?.agent_id, userId]);
    return {
        threads,
        currentThread,
        messages,
        loading,
        error,
        streamingMessage,
        sending,
        createNewThread,
        selectThread,
        deleteThread,
        sendMessage,
        refreshThreads: loadThreads
    };
}
//# sourceMappingURL=useChat.js.map