"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LLMClient = exports.DEFAULT_PROVIDERS = void 0;
// Default LLM providers and models
exports.DEFAULT_PROVIDERS = [
    {
        name: 'OpenAI',
        requiresApiKey: true,
        models: [
            {
                id: 'gpt-4',
                name: 'GPT-4',
                description: 'Most capable model, best for complex tasks',
                maxTokens: 8192
            },
            {
                id: 'gpt-4-turbo-preview',
                name: 'GPT-4 Turbo',
                description: 'Latest GPT-4 model with improved performance',
                maxTokens: 128000
            },
            {
                id: 'gpt-3.5-turbo',
                name: 'GPT-3.5 Turbo',
                description: 'Fast and efficient for most tasks',
                maxTokens: 4096
            }
        ]
    },
    {
        name: 'OpenRouter',
        requiresApiKey: true,
        models: [
            {
                id: 'anthropic/claude-3-opus',
                name: 'Claude 3 Opus',
                description: 'Anthropic\'s most powerful model',
                maxTokens: 200000
            },
            {
                id: 'anthropic/claude-3-sonnet',
                name: 'Claude 3 Sonnet',
                description: 'Balanced performance and speed',
                maxTokens: 200000
            },
            {
                id: 'anthropic/claude-3-haiku',
                name: '<PERSON> 3 <PERSON>ku',
                description: 'Fast and efficient',
                maxTokens: 200000
            },
            {
                id: 'openai/gpt-4',
                name: 'GPT-4 (via OpenRouter)',
                description: 'OpenAI GPT-4 through OpenRouter',
                maxTokens: 8192
            },
            {
                id: 'meta-llama/llama-2-70b-chat',
                name: 'Llama 2 70B Chat',
                description: 'Meta\'s open-source model',
                maxTokens: 4096
            }
        ]
    }
];
class LLMClient {
    constructor(apiKey, provider) {
        this.apiKey = apiKey;
        this.provider = provider;
        switch (provider) {
            case 'OpenAI':
                this.baseUrl = 'https://api.openai.com/v1';
                break;
            case 'OpenRouter':
                this.baseUrl = 'https://openrouter.ai/api/v1';
                break;
            default:
                throw new Error(`Unsupported provider: ${provider}`);
        }
    }
    async createChatCompletion(request) {
        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
        };
        if (this.provider === 'OpenRouter') {
            headers['HTTP-Referer'] = 'https://agent-director.app';
            headers['X-Title'] = 'Agent Director';
        }
        const response = await fetch(`${this.baseUrl}/chat/completions`, {
            method: 'POST',
            headers,
            body: JSON.stringify(request)
        });
        if (!response.ok) {
            const error = await response.text();
            throw new Error(`LLM API error: ${response.status} ${error}`);
        }
        return response.json();
    }
    async *streamChatCompletion(request) {
        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`,
            'Accept': 'text/event-stream'
        };
        if (this.provider === 'OpenRouter') {
            headers['HTTP-Referer'] = 'https://agent-director.app';
            headers['X-Title'] = 'Agent Director';
        }
        const response = await fetch(`${this.baseUrl}/chat/completions`, {
            method: 'POST',
            headers,
            body: JSON.stringify({ ...request, stream: true })
        });
        if (!response.ok) {
            const error = await response.text();
            throw new Error(`LLM API error: ${response.status} ${error}`);
        }
        const reader = response.body?.getReader();
        if (!reader)
            throw new Error('No response body');
        const decoder = new TextDecoder();
        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done)
                    break;
                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split('\n');
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]')
                            return;
                        try {
                            const parsed = JSON.parse(data);
                            const content = parsed.choices?.[0]?.delta?.content;
                            if (content) {
                                yield content;
                            }
                        }
                        catch (e) {
                            // Skip invalid JSON
                        }
                    }
                }
            }
        }
        finally {
            reader.releaseLock();
        }
    }
}
exports.LLMClient = LLMClient;
//# sourceMappingURL=llm.js.map