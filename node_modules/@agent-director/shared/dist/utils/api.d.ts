import { ApiResponse } from '@agent-director/types';
export declare class ApiClient {
    private baseUrl;
    constructor(baseUrl?: string);
    private request;
    get<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>>;
    post<T>(endpoint: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>>;
    put<T>(endpoint: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>>;
    delete<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>>;
}
export declare function streamResponse(response: Response): AsyncGenerator<string, void, unknown>;
export declare function createEventSource(url: string, onMessage: (data: string) => void, onError?: (error: Event) => void): EventSource;
//# sourceMappingURL=api.d.ts.map