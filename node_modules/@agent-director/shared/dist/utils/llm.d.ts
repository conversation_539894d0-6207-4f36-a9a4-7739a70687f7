import { LL<PERSON>rovider } from '@agent-director/types';
export declare const DEFAULT_PROVIDERS: LLMProvider[];
export interface ChatCompletionRequest {
    model: string;
    messages: Array<{
        role: 'system' | 'user' | 'assistant';
        content: string;
    }>;
    stream?: boolean;
    max_tokens?: number;
    temperature?: number;
}
export interface ChatCompletionResponse {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: Array<{
        index: number;
        message: {
            role: string;
            content: string;
        };
        finish_reason: string;
    }>;
    usage?: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}
export declare class LLMClient {
    private apiKey;
    private provider;
    private baseUrl;
    constructor(apiKey: string, provider: string);
    createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;
    streamChatCompletion(request: ChatCompletionRequest): AsyncGenerator<string, void, unknown>;
}
//# sourceMappingURL=llm.d.ts.map