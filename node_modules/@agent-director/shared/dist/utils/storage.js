"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElectronStoreAdapter = exports.LocalStorageAdapter = void 0;
exports.encryptApiKey = encryptApiKey;
exports.decryptApiKey = decryptApiKey;
// Web localStorage adapter
class LocalStorageAdapter {
    async getItem(key) {
        return localStorage.getItem(key);
    }
    async setItem(key, value) {
        localStorage.setItem(key, value);
    }
    async removeItem(key) {
        localStorage.removeItem(key);
    }
    async clear() {
        localStorage.clear();
    }
}
exports.LocalStorageAdapter = LocalStorageAdapter;
// Electron store adapter (for desktop)
class ElectronStoreAdapter {
    constructor(store) {
        this.store = store;
    }
    async getItem(key) {
        return this.store.get(key) || null;
    }
    async setItem(key, value) {
        this.store.set(key, value);
    }
    async removeItem(key) {
        this.store.delete(key);
    }
    async clear() {
        this.store.clear();
    }
}
exports.ElectronStoreAdapter = ElectronStoreAdapter;
// Utility functions
async function encryptApiKey(apiKey, secretKey) {
    // Simple encryption for demo - in production, use proper encryption
    const encoder = new TextEncoder();
    const data = encoder.encode(apiKey);
    const key = await crypto.subtle.importKey('raw', encoder.encode(secretKey.padEnd(32, '0').slice(0, 32)), { name: 'AES-GCM' }, false, ['encrypt']);
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const encrypted = await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, key, data);
    const result = new Uint8Array(iv.length + encrypted.byteLength);
    result.set(iv);
    result.set(new Uint8Array(encrypted), iv.length);
    return btoa(String.fromCharCode(...result));
}
async function decryptApiKey(encryptedApiKey, secretKey) {
    try {
        const data = new Uint8Array(atob(encryptedApiKey).split('').map(c => c.charCodeAt(0)));
        const iv = data.slice(0, 12);
        const encrypted = data.slice(12);
        const encoder = new TextEncoder();
        const key = await crypto.subtle.importKey('raw', encoder.encode(secretKey.padEnd(32, '0').slice(0, 32)), { name: 'AES-GCM' }, false, ['decrypt']);
        const decrypted = await crypto.subtle.decrypt({ name: 'AES-GCM', iv }, key, encrypted);
        return new TextDecoder().decode(decrypted);
    }
    catch (error) {
        throw new Error('Failed to decrypt API key');
    }
}
//# sourceMappingURL=storage.js.map