export interface StorageAdapter {
    getItem(key: string): Promise<string | null>;
    setItem(key: string, value: string): Promise<void>;
    removeItem(key: string): Promise<void>;
    clear(): Promise<void>;
}
export declare class LocalStorageAdapter implements StorageAdapter {
    getItem(key: string): Promise<string | null>;
    setItem(key: string, value: string): Promise<void>;
    removeItem(key: string): Promise<void>;
    clear(): Promise<void>;
}
export declare class ElectronStoreAdapter implements StorageAdapter {
    private store;
    constructor(store: any);
    getItem(key: string): Promise<string | null>;
    setItem(key: string, value: string): Promise<void>;
    removeItem(key: string): Promise<void>;
    clear(): Promise<void>;
}
export declare function encryptApiKey(apiKey: string, secretKey: string): Promise<string>;
export declare function decryptApiKey(encryptedApiKey: string, secretKey: string): Promise<string>;
//# sourceMappingURL=storage.d.ts.map