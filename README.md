# Agent Director MVP

Agent Director is an application designed to empower users to create, configure, manage, and share AI agents. Users can define an agent's behavior by selecting AI models, crafting system instructions, and then interact with these agents through a chat interface.

## Features

### Core Functionality
- **Agent Creation**: Create custom AI agents with specific models and system instructions
- **Multi-Provider Support**: Supports OpenAI and OpenRouter APIs
- **Chat Interface**: Interactive chat with streaming responses
- **Conversation Management**: Multiple conversation threads per agent
- **Cross-Platform**: Both desktop (Electron) and web applications

### Desktop Application
- Local SQLite database storage
- No user authentication required
- Offline-capable (except for LLM API calls)
- Native desktop experience

### Web Application
- Google OAuth authentication
- Cloud storage via Supabase
- Agent sharing capabilities
- Responsive web interface

## Project Structure

```
agent-director/
├── packages/
│   ├── types/          # Shared TypeScript types
│   ├── shared/         # Shared React components and utilities
│   ├── web/           # Next.js web application
│   └── desktop/       # Electron desktop application
├── specs/
│   └── mvp_prd.md     # Product Requirements Document
└── README.md
```

## Setup Instructions

### Prerequisites
- Node.js 18+ 
- npm or yarn
- For web app: Supabase account
- API keys for OpenAI and/or OpenRouter

### Installation

1. **Clone and install dependencies**:
```bash
git clone <repository-url>
cd agent-director
npm install --legacy-peer-deps
```

2. **Build shared packages**:
```bash
cd packages/types && npm run build
cd ../shared && npm run build
```

### Web Application Setup

1. **Create Supabase project**:
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Run the SQL schema from `packages/web/supabase-schema.sql`
   - Enable Google OAuth in Authentication settings

2. **Configure environment**:
```bash
cd packages/web
cp .env.local.example .env.local
# Edit .env.local with your Supabase credentials
```

3. **Run the web application**:
```bash
npm run dev
```

The web app will be available at `http://localhost:3000`

### Desktop Application Setup

1. **Install desktop dependencies**:
```bash
cd packages/desktop
npm install --legacy-peer-deps
```

2. **Run the desktop application**:
```bash
npm run dev
```

## Usage

### Creating an Agent

1. Click "Create New Agent" or the "+" button
2. Fill in the agent details:
   - **Name**: Give your agent a descriptive name
   - **Provider**: Choose OpenAI or OpenRouter
   - **Model**: Select from available models
   - **API Key**: Enter your API key for the selected provider
   - **System Instructions**: Define the agent's behavior and personality

### Chatting with Agents

1. Select an agent from the list
2. Switch to the "Chat" tab
3. Start typing messages to interact with your agent
4. Create new conversation threads as needed

### Sharing Agents (Web Only)

1. Click the share button on an agent
2. Choose sharing mode:
   - **Use Mode**: Others can chat with your agent
   - **View & Clone Mode**: Others can see the configuration and clone it

## API Keys

You'll need API keys from one or more of these providers:

- **OpenAI**: Get from [platform.openai.com](https://platform.openai.com)
- **OpenRouter**: Get from [openrouter.ai](https://openrouter.ai)

## Development

### Running Tests
```bash
npm test
```

### Building for Production

**Web Application**:
```bash
cd packages/web
npm run build
```

**Desktop Application**:
```bash
cd packages/desktop
npm run build
npm run dist  # Creates distributable packages
```

## Architecture

### Shared Components
- **Types Package**: Common TypeScript interfaces and types
- **Shared Package**: Reusable React components, hooks, and utilities

### Database Abstraction
Both applications use a common `DatabaseAdapter` interface:
- **Web**: Supabase implementation with PostgreSQL
- **Desktop**: SQLite implementation

### LLM Integration
- Unified LLM client supporting multiple providers
- Streaming response handling
- Secure API key management

## Security Considerations

- API keys are encrypted when stored (web application)
- Row-level security enabled in Supabase
- Context isolation in Electron application
- No API keys exposed to client-side code in shared mode

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

[Add your license here]

## Support

For issues and questions, please create an issue in the GitHub repository.
